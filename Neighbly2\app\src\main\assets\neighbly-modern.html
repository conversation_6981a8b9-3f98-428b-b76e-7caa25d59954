<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Neighbly - Современный интерфейс</title>
    
    <!-- Google Sans Fonts -->
    <style>
        @font-face {
            font-family: 'Google Sans';
            src: url('GoogleFonts/GoogleSans-Regular.ttf') format('truetype');
            font-weight: 400;
            font-style: normal;
        }
        @font-face {
            font-family: 'Google Sans';
            src: url('GoogleFonts/GoogleSans-Medium.ttf') format('truetype');
            font-weight: 500;
            font-style: normal;
        }
        @font-face {
            font-family: 'Google Sans';
            src: url('GoogleFonts/GoogleSans-Bold.ttf') format('truetype');
            font-weight: 700;
            font-style: normal;
        }
        @font-face {
            font-family: 'Google Sans';
            src: url('GoogleFonts/GoogleSans-Italic.ttf') format('truetype');
            font-weight: 400;
            font-style: italic;
        }
        @font-face {
            font-family: 'Google Sans';
            src: url('GoogleFonts/GoogleSans-MediumItalic.ttf') format('truetype');
            font-weight: 500;
            font-style: italic;
        }
        @font-face {
            font-family: 'Google Sans';
            src: url('GoogleFonts/GoogleSans-BoldItalic.ttf') format('truetype');
            font-weight: 700;
            font-style: italic;
        }
    </style>
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />

    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        :root {
            --primary-color: #2d5016;
            --primary-dark: #1a2f0c;
            --primary-light: #4a7c23;
            --accent-color: #ff6b35;
            --danger-color: #dc3545;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --light-gray: #f8f9fa;
            --medium-gray: #e9ecef;
            --dark-gray: #666;
            --text-color: #333;
            --shadow-sm: 0 2px 4px rgba(0,0,0,0.08);
            --shadow-md: 0 4px 12px rgba(0,0,0,0.12);
            --shadow-lg: 0 8px 24px rgba(0,0,0,0.16);
            --shadow-xl: 0 12px 48px rgba(0,0,0,0.24);
            --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        body {
            font-family: 'Google Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            touch-action: manipulation;
            -webkit-user-select: none;
            -webkit-touch-callout: none;
            -webkit-tap-highlight-color: transparent;
        }

        /* Разрешаем выделение текста для полей ввода */
        input, textarea {
            -webkit-user-select: auto !important;
            -moz-user-select: auto !important;
            -ms-user-select: auto !important;
            user-select: auto !important;
            pointer-events: auto !important;
        }

        .phone-frame {
            width: 100vw;
            height: 100vh;
            background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
            overflow: hidden;
            position: relative;
            touch-action: manipulation;
            box-shadow: inset 0 0 100px rgba(45, 80, 22, 0.03);
        }
        
        .screen {
            width: 100%;
            height: 100%;
            display: none;
            flex-direction: column;
        }
        
        .screen.active {
            display: flex;
        }
        
        /* Registration Screen */
        .registration-screen {
            justify-content: center;
            align-items: center;
            padding: 40px;
            background: white;
        }

        .reg-title {
            text-align: center;
            margin-bottom: 60px;
            color: #333;
            line-height: 1.3;
        }

        .reg-title-small {
            font-size: 20px;
            font-weight: 400;
            margin-bottom: 10px;
        }

        .reg-title-large {
            font-size: 32px;
            font-weight: 700;
        }
        
        .input {
            width: 100%;
            height: 56px;
            border: 1px solid #e0e0e0;
            border-radius: 16px;
            padding: 0 20px;
            margin-bottom: 20px;
            font-size: 16px;
            font-family: 'Google Sans', sans-serif;
            font-weight: 400;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }

        .input:focus {
            outline: none;
            background: white;
            border-color: #2d5016;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(45, 80, 22, 0.1);
        }

        .reg-button {
            width: 100%;
            height: 56px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border: none;
            border-radius: 16px;
            color: white;
            font-size: 18px;
            font-weight: 600;
            font-family: 'Google Sans', sans-serif;
            margin-top: 20px;
            cursor: pointer;
            transition: var(--transition-normal);
            box-shadow: 0 4px 15px rgba(45, 80, 22, 0.3);
            position: relative;
            overflow: hidden;
        }

        .reg-button::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }

        .reg-button:hover::before {
            width: 300px;
            height: 300px;
        }

        .reg-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(45, 80, 22, 0.4);
        }

        .reg-button:active {
            transform: translateY(0) scale(0.98);
            box-shadow: 0 2px 10px rgba(45, 80, 22, 0.3);
        }

        .login-link-button {
            width: 100%;
            height: 48px;
            background: transparent;
            border: 2px solid #2d5016;
            border-radius: 16px;
            color: #2d5016;
            font-size: 16px;
            font-weight: 500;
            font-family: 'Google Sans', sans-serif;
            margin-top: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .login-link-button:hover {
            background: #2d5016;
            color: white;
        }

        .login-link-button:active {
            transform: translateY(2px) scale(0.98);
        }

        /* Google Sign-In Button */
        .google-signin-btn {
            width: 100%;
            height: 48px;
            background: transparent;
            border: 2px solid #2d5016;
            border-radius: 16px;
            color: #2d5016;
            font-size: 16px;
            font-weight: 500;
            font-family: 'Google Sans', sans-serif;
            margin-top: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }

        .google-signin-btn:hover {
            background: #2d5016;
            color: white;
        }

        .google-signin-btn:hover .google-logo path {
            /* Keep Google logo colors on hover */
        }

        .google-signin-btn:active {
            transform: translateY(2px) scale(0.98);
        }

        .google-logo {
            width: 20px;
            height: 20px;
            display: inline-block;
        }

        .error-message {
            background: #ffebee;
            color: #c62828;
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            font-family: 'Google Sans', sans-serif;
            border-left: 4px solid #c62828;
            display: none;
        }

        .success-message {
            background: #e8f5e8;
            color: #2d5016;
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            font-family: 'Google Sans', sans-serif;
            border-left: 4px solid #2d5016;
            display: none;
        }
        
        /* Main App Layout */
        .app-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            position: relative;
            overflow: hidden; /* Предотвращает выход контента за границы */
        }

        /* Neighbly specific elements */
        .neighbly-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            z-index: 100;
            pointer-events: none;
        }

        .neighbly-overlay > * {
            pointer-events: auto;
        }

        .greeting-weather-row {
            display: flex;
            align-items: center;
            padding: 60px 20px 0;
            gap: 0;
        }

        .greeting {
            flex: 0.7;
            font-size: 36px;
            font-weight: 600;
            color: #666;
            text-shadow: 0 2px 4px rgba(255,255,255,0.8);
        }

        .weather {
            flex: 0.3;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: 8px;
            color: #666;
            font-size: 32px;
            font-weight: 500;
            text-shadow: 0 2px 4px rgba(255,255,255,0.8);
        }

        .weather-icon {
            font-size: 36px;
        }

        /* Language Button */
        .language-btn {
            position: absolute;
            top: 60px;
            right: 20px;
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 200;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .language-btn:hover {
            background: white;
            transform: scale(1.1);
        }

        .language-btn .material-icons {
            font-size: 20px;
            color: #2d5016;
        }

        /* ArchNeighbly specific elements */
        .arch-top-bar {
            display: none;
            justify-content: space-between;
            align-items: center;
            padding: 20px 20px 20px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            position: relative;
            z-index: 10;
        }

        .menu-btn, .add-chat-btn {
            width: 40px;
            height: 40px;
            border: none;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition-normal);
            box-shadow: var(--shadow-sm);
        }

        .menu-btn:hover, .add-chat-btn:hover {
            background: white;
            transform: scale(1.1) rotate(5deg);
            box-shadow: var(--shadow-md);
        }

        .menu-btn:active, .add-chat-btn:active {
            transform: scale(0.95);
        }

        .arch-header-title {
            font-size: 18px;
            font-weight: 600;
            color: #4a4a4a;
            font-family: 'Google Sans', sans-serif;
            opacity: 0;
            transform: scale(1) translateY(0);
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .arch-header-title.show {
            opacity: 1;
        }

        .arch-content-title {
            font-size: 32px;
            font-weight: 700;
            color: #4a4a4a;
            margin-bottom: 20px;
            font-family: 'Google Sans', sans-serif;
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            transform: scale(1) translateY(0);
        }

        .arch-content-title.animate-up {
            opacity: 0;
            transform: scale(0.56) translateY(-280px);
        }

        .arch-content-text {
            font-size: 16px;
            line-height: 1.6;
            color: #666;
            margin-bottom: 20px;
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            opacity: 1;
        }

        .arch-content-text.hide {
            opacity: 0;
        }

        .arch-content {
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .arch-content.hide {
            opacity: 0;
            visibility: hidden;
        }
        
        .content-area {
            flex: 1;
            overflow: hidden;
            position: relative;
        }

        .tab-content {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            opacity: 1;
            transform: translateX(0);
            transition: all 0.3s ease;
        }

        .tab-content:not(.active) {
            opacity: 0;
            transform: translateX(-100%);
        }
        
        /* Chat Messages */
        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            max-height: calc(100vh - 300px);
            min-height: 200px;
        }

        .message {
            margin-bottom: 15px;
            padding: 12px 16px;
            border-radius: 18px;
            max-width: 80%;
            word-wrap: break-word;
        }

        .message.user {
            background: #2d5016;
            color: white;
            margin-left: auto;
            text-align: right;
        }

        .message.bot {
            background: #f0f0f0;
            color: #333;
            margin-right: auto;
        }

        /* Chat Messages */
        .chat-messages {
            position: absolute;
            top: 80px; /* Уменьшенный отступ под заголовок ArchNeighbly */
            left: 0;
            right: 0;
            bottom: 120px; /* Отступ до увеличенной строки запроса */
            padding: 20px;
            overflow-y: auto;
            overflow-x: hidden;
            scrollbar-width: none;
            -ms-overflow-style: none;
            box-sizing: border-box;
            z-index: 1;
            touch-action: pan-y;
            -webkit-overflow-scrolling: touch;
            scroll-behavior: smooth;
        }

        .chat-messages::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
        }

        /* Градиентное перекрытие для сообщений внизу */
        .chat-overlay {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 140px; /* Увеличенная высота до строки запроса + немного больше */
            background: linear-gradient(to top, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0.95) 60%, rgba(255, 255, 255, 0) 100%);
            pointer-events: none;
            z-index: 1500;
        }

        .message {
            margin-bottom: 20px; /* Увеличенный отступ */
            padding: 12px 16px 16px 16px; /* Увеличенный нижний padding */
            border-radius: 18px;
            max-width: 85%;
            word-wrap: break-word;
            overflow-wrap: break-word;
            hyphens: auto;
            font-family: 'Google Sans', sans-serif;
            line-height: 1.4;
            position: relative;
            max-height: 6000px;
            overflow: hidden;
        }

        /* Разделительная линия после каждого сообщения */
        .message::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            right: 0;
            width: 100%;
            height: 1px;
            background: #e0e0e0;
        }

        /* Ограничение высоты для длинных сообщений */
        .message.collapsed {
            max-height: 6000px;
            overflow: hidden;
        }

        .message.collapsed::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 40px;
            background: linear-gradient(transparent, currentColor);
            pointer-events: none;
        }

        .message.bot.collapsed::after {
            background: linear-gradient(transparent, #2d5016);
        }

        .message.user.collapsed::after {
            background: linear-gradient(transparent, #f0f0f0);
        }

        /* Кнопка "Показать больше" */
        .expand-btn {
            display: inline-block;
            margin-top: 8px;
            padding: 4px 8px;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            color: inherit;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .message.user .expand-btn {
            background: rgba(0, 0, 0, 0.1);
            border-color: rgba(0, 0, 0, 0.2);
        }

        .expand-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        .message.user .expand-btn:hover {
            background: rgba(0, 0, 0, 0.2);
        }

        .message.user {
            background: linear-gradient(135deg, #f5f7fa 0%, #e9ecef 100%);
            color: #333;
            margin-right: auto;
            text-align: left;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }

        .message.bot {
            background: transparent;
            color: #333;
            margin: 0;
            width: 100%;
            max-width: 100%;
            text-align: left;
            animation: slideInLeft 0.5s ease-out;
            box-shadow: none;
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Loading indicator */
        .loading-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin: 10px 0;
            opacity: 0;
            animation: fadeIn 0.3s ease-in forwards;
        }

        .loading-dot {
            width: 10px;
            height: 10px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-radius: 50%;
            animation: bounce 1.4s ease-in-out infinite;
            box-shadow: 0 2px 4px rgba(45, 80, 22, 0.2);
        }

        .loading-dot:nth-child(1) { animation-delay: 0s; }
        .loading-dot:nth-child(2) { animation-delay: 0.2s; }
        .loading-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes bounce {
            0%, 80%, 100% {
                transform: translateY(0) scale(1);
            }
            40% {
                transform: translateY(-10px) scale(1.2);
            }
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 0.4;
            }
            50% {
                opacity: 1;
            }
        }

        @keyframes fadeIn {
            to {
                opacity: 1;
            }
        }

        @keyframes fadeOut {
            to {
                opacity: 0;
            }
        }

        /* Chat Input */
        .chat-input-container {
            position: fixed;
            bottom: 70px;
            left: 0;
            right: 0;
            background: linear-gradient(to top, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0.95) 70%, rgba(255, 255, 255, 0) 100%);
            padding: 15px 20px;
            z-index: 2000 !important;
            transition: all 0.3s ease;
            pointer-events: auto !important;
            -webkit-user-select: auto;
            -moz-user-select: auto;
            -ms-user-select: auto;
            user-select: auto;
        }

        .chat-input-container.with-image {
            bottom: 110px;
        }

        .chat-input {
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
            border-radius: 50px; /* Полный полукруг */
            padding: 12px 16px; /* Увеличенный padding */
            gap: 12px; /* Увеличенный gap */
            border: none;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            min-height: 50px; /* Увеличенная высота */
            transition: var(--transition-normal);
            position: relative;
            pointer-events: auto !important;
            -webkit-user-select: auto;
            -moz-user-select: auto;
            -ms-user-select: auto;
            user-select: auto;
            z-index: 2001;
        }

        .chat-input:focus-within {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(45, 80, 22, 0.15);
        }

        .chat-input.with-image {
            min-height: 80px;
            align-items: flex-start;
            padding: 16px;
            border-radius: 20px;
        }

        .chat-input input[type="text"] {
            flex: 1;
            border: none;
            background: transparent;
            font-size: 16px;
            outline: none;
            font-family: 'Google Sans', sans-serif;
            color: #333;
            pointer-events: auto !important;
            cursor: text !important;
            -webkit-user-select: auto !important;
            -moz-user-select: auto !important;
            -ms-user-select: auto !important;
            user-select: auto !important;
            z-index: 2002;
            position: relative;
        }

        .chat-input input[type="text"]::placeholder {
            color: #999;
        }

        /* Image attachment styles */
        .image-attachment-container {
            display: flex;
            flex-direction: column;
            flex: 1;
            gap: 8px;
            pointer-events: auto !important;
            -webkit-user-select: auto;
            -moz-user-select: auto;
            -ms-user-select: auto;
            user-select: auto;
            z-index: 2001;
        }

        .attached-image {
            position: relative;
            display: inline-block;
            max-width: 120px;
            max-height: 80px;
            border-radius: 12px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .attached-image:hover {
            transform: scale(1.05);
        }

        .attached-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
        }

        .remove-image-btn {
            position: absolute;
            top: -8px;
            right: -8px;
            width: 24px;
            height: 24px;
            background: #dc3545;
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            z-index: 10;
            transition: all 0.3s ease;
        }

        .remove-image-btn:hover {
            background: #c82333;
            transform: scale(1.1);
        }

        /* Message image styles */
        .message-image {
            margin-bottom: 8px;
        }

        .message-image img {
            max-width: 200px;
            max-height: 150px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            object-fit: cover;
        }

        .message-image img:hover {
            transform: scale(1.05);
        }

        .message-text {
            margin-top: 4px;
        }

        /* Стили для форматированных сообщений бота */
        .message.bot p {
            margin: 0 0 12px 0;
            line-height: 1.6;
        }

        .message.bot p:last-child {
            margin-bottom: 0;
        }

        .message.bot br {
            margin: 4px 0;
        }

        /* Стили для жирного текста в сообщениях бота */
        .message.bot strong,
        .message.bot b {
            color: var(--primary-color);
            font-weight: 600;
        }

        /* Контейнер для горизонтального скролла таблиц */
        .table-container {
            overflow-x: auto;
            overflow-y: hidden;
            margin: 12px 0;
            border-radius: 12px;
            border: none;
            background: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            width: 100%;
            touch-action: pan-x; /* Только горизонтальная прокрутка */
            -webkit-overflow-scrolling: touch;
        }

        /* Стили для таблиц */
        .message.bot table {
            width: auto;
            min-width: 100%;
            border-collapse: collapse;
            margin: 0;
            font-size: 14px;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            white-space: nowrap;
        }

        .message.bot table th,
        .message.bot table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #e8e8e8;
            white-space: nowrap;
            vertical-align: top;
            min-width: 80px;
        }

        .message.bot table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: var(--primary-color);
            border-bottom: 2px solid #e0e0e0;
        }

        .message.bot table tr:last-child td {
            border-bottom: none;
        }

        .message.bot table tr:hover {
            background: #f9f9f9;
        }
            min-width: 100%;
            white-space: nowrap;
        }

        /* Стили для списков */
        .message.bot ul,
        .message.bot ol {
            margin: 12px 0;
            padding-left: 20px;
        }

        .message.bot li {
            margin: 4px 0;
            line-height: 1.5;
        }

        /* Стили для кода */
        .message.bot code {
            background-color: #f1f3f4;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }

        .message.bot pre {
            background-color: #f8f9fa;
            padding: 12px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 12px 0;
        }

        .message.bot pre code {
            background: none;
            padding: 0;
        }

        /* Стили для заголовков в сообщениях бота */
        .message.bot h1,
        .message.bot h2,
        .message.bot h3 {
            color: var(--primary-color);
            margin: 16px 0 8px 0;
            font-weight: 600;
        }

        .message.bot h1 {
            font-size: 20px;
        }

        .message.bot h2 {
            font-size: 18px;
        }

        .message.bot h3 {
            font-size: 16px;
        }

        /* Стили для списков в сообщениях бота */
        .message.bot ul,
        .message.bot ol {
            margin: 12px 0;
            padding-left: 20px;
        }

        .message.bot li {
            margin: 4px 0;
            line-height: 1.5;
        }
        
        .attach-btn, .send-btn {
            width: 28px; /* Уменьшенный размер */
            height: 28px; /* Уменьшенный размер */
            border: none;
            background: transparent;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer !important;
            transition: all 0.2s ease;
            position: relative;
            pointer-events: auto !important;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            z-index: 2002;
        }
        
        .attach-btn:hover, .send-btn:hover {
            background: var(--primary-color);
            transform: scale(1.05);
        }

        .attach-btn:hover .material-icons,
        .send-btn:hover .material-icons {
            color: white;
        }

        .attach-btn:active, .send-btn:active {
            transform: scale(0.95);
        }
        
        .chat-input input {
            flex: 1;
            border: none;
            background: transparent;
            font-size: 16px;
            outline: none;
            font-family: 'Google Sans', sans-serif;
        }

        .chat-input input::placeholder {
            font-family: 'Google Sans', sans-serif;
        }


        
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 15px;
            left: 15px;
            right: 15px;
            display: flex;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 50px; /* Полный полукруг */
            padding: 12px 8px; /* Увеличенный padding */
            gap: 0;
            z-index: 3000;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.2);
            height: 60px; /* Увеличенная высота */
        }

        .nav-item {
            flex: 1;
            height: 100%; /* Занимает всю высоту контейнера */
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            background: transparent;
            border-radius: 20px;
            position: relative;
        }

        .nav-item.active {
            transform: scale(1.1);
            background: transparent;
        }

        .nav-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #2d5016;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .nav-item.has-avatar {
            position: relative;
        }

        .nav-item.has-avatar .material-icons {
            display: none !important;
        }

        .nav-icon {
            font-size: 24px; /* Увеличенный размер иконок */
            font-weight: 600;
            color: #999;
            transition: all 0.3s ease;
        }

        .nav-item.active .nav-icon {
            color: #2d5016;
        }

        .nav-item.active::after {
            content: '';
            position: absolute;
            bottom: 2px;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
            border-radius: 2px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; width: 20px; }
            50% { opacity: 0.7; width: 25px; }
        }

        /* Thinking process styles (если захотите показывать размышления) */
        .thinking-process {
            background: rgba(45, 80, 22, 0.1);
            border-left: 3px solid #2d5016;
            padding: 8px 12px;
            margin: 8px 0;
            border-radius: 4px;
            font-size: 12px;
            color: #666;
            font-style: italic;
            font-family: 'Google Sans', sans-serif;
        }

        /* Map Container */
        .map-container {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1;
        }

        #map {
            width: 100%;
            height: 100%;
        }

        /* Hide Leaflet attribution and controls */
        .leaflet-control-attribution {
            display: none !important;
        }

        .leaflet-control-zoom {
            display: none !important;
        }

        .leaflet-control {
            display: none !important;
        }
        
        /* ArchNeighbly Content */
        .arch-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            width: 80%;
        }

        .arch-title {
            font-size: 32px;
            font-weight: 700;
            color: #4a4a4a;
            margin-bottom: 20px;
            font-family: 'Google Sans', sans-serif;
        }

        /* Highlight for registration */
        .reg-title .highlight {
            background: linear-gradient(45deg, #2d5016, #4a7c23);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 600;
        }

        .arch-text {
            font-size: 16px;
            line-height: 1.6;
            color: #666;
            margin-bottom: 20px;
        }
        
        .more-link {
            color: #2d5016;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .more-link:hover {
            color: #1a2f0c;
            transform: scale(1.05);
        }
        
        /* Side Menu */
        .side-menu-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 5000;
        }
        
        .side-menu-overlay.show {
            opacity: 1;
            visibility: visible;
        }
        
        .side-menu {
            position: absolute;
            top: 0;
            left: 0;
            width: 75%;
            height: 100%;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            padding: 60px 20px 20px;
            transform: translateX(-100%);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            flex-direction: column;
            box-shadow: 4px 0 30px rgba(0,0,0,0.2);
            border-right: 1px solid rgba(45, 80, 22, 0.1);
            overflow: visible;
            z-index: 5001;
        }
        
        .side-menu-overlay.show .side-menu {
            transform: translateX(0);
        }
        
        .side-menu h3 {
            text-align: left;
            font-size: 24px;
            font-weight: 600;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 30px;
            font-family: 'Google Sans', sans-serif;
            padding-bottom: 15px;
            border-bottom: 2px solid transparent;
            border-image: linear-gradient(90deg, var(--primary-color), var(--primary-light));
            border-image-slice: 1;
            position: relative;
        }

        .side-menu h3::after {
            content: '💬';
            position: absolute;
            right: 0;
            top: 0;
            font-size: 20px;
            -webkit-text-fill-color: initial;
        }

        .chat-list {
            flex: 1;
            overflow-y: auto;
            overflow-x: visible;
            scrollbar-width: none;
            -ms-overflow-style: none;
            display: flex;
            flex-direction: column;
            min-height: 100px;
            padding-right: 15px;
            position: relative;
            z-index: 5;
        }

        .chat-list::-webkit-scrollbar {
            display: none;
        }

        .chat-item {
            padding: 14px;
            border-radius: 12px;
            margin-bottom: 12px;
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(248,249,250,0.9));
            backdrop-filter: blur(10px);
            cursor: pointer;
            transition: var(--transition-normal);
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            position: relative;
            overflow: visible;
            z-index: 10;
        }

        .chat-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(45, 80, 22, 0.1), transparent);
            transition: left 0.5s;
        }

        .chat-item:hover::before {
            left: 100%;
        }

        .chat-item:hover {
            background: linear-gradient(135deg, #ffffff, #f0f9ff);
            transform: translateX(8px);
            box-shadow: 0 4px 15px rgba(45, 80, 22, 0.15);
            z-index: 20;
        }

        .chat-title {
            font-weight: 600;
            color: #333;
            font-size: 14px;
            margin-bottom: 4px;
            font-family: 'Google Sans', sans-serif;
        }

        .chat-timestamp {
            font-size: 12px;
            color: #666;
            font-family: 'Google Sans', sans-serif;
        }

        .no-chats {
            text-align: center;
            color: #999;
            font-style: normal;
            padding: 40px 20px;
            font-family: 'Google Sans', sans-serif;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            flex: 1;
            margin: auto;
            font-size: 16px;
            opacity: 0.7;
        }

        .no-chats::before {
            content: '📭';
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }

        .chat-content {
            flex: 1;
        }

        .delete-chat-btn {
            background: none;
            border: none;
            color: #2d5016;
            font-size: 18px;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.3s ease;
            margin-left: 8px;
        }

        .delete-chat-btn:hover {
            background: rgba(45, 80, 22, 0.1);
            transform: scale(1.1);
        }

        .delete-chat-btn .material-icons {
            font-size: 18px;
        }

        /* Delete Confirmation Dialog */
        .delete-dialog-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 3000;
        }

        .delete-dialog-overlay.show {
            display: flex;
        }

        .delete-dialog {
            background: white;
            border-radius: 12px;
            padding: 24px;
            width: 90%;
            max-width: 320px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            text-align: center;
        }

        .delete-dialog-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            font-family: 'Google Sans', sans-serif;
        }

        .delete-dialog-buttons {
            display: flex;
            gap: 12px;
            justify-content: center;
        }

        .delete-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            font-family: 'Google Sans', sans-serif;
            transition: all 0.3s ease;
        }

        .delete-btn.cancel {
            background: #2d5016;
            color: white;
        }

        .delete-btn.confirm {
            background: white;
            color: #2d5016;
            border: 2px solid #2d5016;
        }

        .delete-btn:hover {
            transform: translateY(-2px);
        }
        
        /* Profile Menu */
        .profile-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 5000;
            pointer-events: none;
        }

        .profile-overlay.show {
            opacity: 1;
            visibility: visible;
            pointer-events: auto;
        }

        .profile-menu {
            position: absolute;
            top: 0;
            right: 0;
            width: 70%;
            height: 100%;
            background: white;
            border-radius: 20px 0 0 20px;
            padding: 60px 20px 20px;
            transform: translateX(100%);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden; /* Убираем прокрутку */
            display: flex;
            flex-direction: column;
            z-index: 5001;
        }

        .profile-menu::-webkit-scrollbar {
            display: none;
        }

        .profile-overlay.show .profile-menu {
            transform: translateX(0);
        }

        /* Profile Header Styles */
        .profile-header {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 20px;
            flex-shrink: 0;
        }

        .profile-avatar-container {
            position: relative;
            margin-bottom: 15px;
        }

        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #f5f7fa 0%, #e9ecef 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            border: 3px solid transparent;
            background-clip: padding-box;
            position: relative;
            box-shadow: 0 4px 15px rgba(45, 80, 22, 0.2);
        }

        .profile-avatar::before {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            right: -3px;
            bottom: -3px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-radius: 50%;
            z-index: -1;
        }

        .profile-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .profile-avatar .material-icons {
            font-size: 36px;
            color: #666;
        }

        .profile-edit-avatar-btn {
            position: absolute;
            bottom: -5px;
            right: -5px;
            width: 28px;
            height: 28px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition-normal);
            border: 2px solid white;
            box-shadow: 0 2px 8px rgba(45, 80, 22, 0.3);
        }

        .profile-edit-avatar-btn:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
            transform: scale(1.15) rotate(90deg);
        }

        .profile-edit-avatar-btn:active {
            transform: scale(0.95);
        }

        .profile-edit-avatar-btn .material-icons {
            font-size: 16px;
            color: white;
        }

        .profile-username {
            font-size: 20px;
            font-weight: 600;
            color: #2d5016;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 8px;
            transition: all 0.3s ease;
            text-align: center;
            min-width: 120px;
        }

        .profile-username:hover {
            background: rgba(45, 80, 22, 0.1);
        }

        .profile-username.editing {
            background: transparent;
            border: none;
            outline: none;
        }

        /* Profile Divider */
        .profile-divider {
            width: 100%;
            height: 1px;
            background: #e0e0e0;
            margin: 20px 0;
            flex-shrink: 0;
        }

        /* Profile Menu Items */
        .profile-menu-items {
            flex: 1;
            overflow-y: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .profile-menu-items::-webkit-scrollbar {
            display: none;
        }

        .profile-item {
            padding: 16px 0;
            border-bottom: 1px solid #f0f0f0;
            font-size: 16px;
            color: #666;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .profile-item:hover {
            color: #2d5016;
            padding-left: 10px;
        }

        .account-action:hover {
            color: #dc3545 !important;
            padding-left: 30px !important;
            transform: translateX(5px);
        }

        .profile-item:last-child {
            border-bottom: none;
        }

        .account-section {
            color: #666 !important;
        }

        .account-arrow {
            transition: transform 0.3s ease;
        }

        .account-arrow.rotated {
            transform: rotate(180deg);
        }

        .account-items {
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .account-items.show {
            display: block !important;
        }

        /* Full Screen Pages */
        .full-screen-page {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: white;
            z-index: 4000;
            display: none;
            flex-direction: column;
            transform: translateX(100%);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .full-screen-page.show {
            display: flex;
            transform: translateX(0);
        }

        .page-header {
            display: flex;
            align-items: center;
            padding: 60px 20px 20px;
            border-bottom: 1px solid #f0f0f0;
        }

        .back-arrow {
            width: 40px;
            height: 40px;
            border: none;
            background: #f8f9fa;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 15px;
        }

        .back-arrow:hover {
            background: #e9ecef;
            transform: scale(1.05);
        }

        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }

        .page-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
            display: flex;
            justify-content: center;
            align-items: flex-start;
        }

        .page-content::-webkit-scrollbar {
            display: none;
        }

        .page-text {
            font-size: 16px;
            line-height: 1.6;
            color: #666;
            text-align: center;
            width: 100%;
            max-width: 320px;
        }

        .page-text p {
            margin-bottom: 24px;
        }

        .page-text p:last-child {
            margin-bottom: 0;
        }

        /* Profile Pages Styles */
        .current-value {
            text-align: center;
            font-size: 18px;
            color: #333;
            margin-bottom: 30px;
            padding: 20px;
        }

        .avatar-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 40px 20px;
        }

        .avatar-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 30px;
            overflow: hidden;
            border: 3px solid #2d5016;
        }

        .avatar-circle img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .avatar-circle .material-icons {
            font-size: 48px;
            color: #666;
        }

        .change-photo-btn {
            background: #2d5016;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            font-family: 'Google Sans', sans-serif;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .change-photo-btn:hover {
            background: #1a2f0c;
            transform: translateY(-2px);
        }

        /* Marker Dialog Styles */
        .marker-dialog {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 3000;
            pointer-events: none;
        }

        .marker-dialog.show {
            display: flex;
            pointer-events: auto;
        }

        .marker-dialog-content {
            background: white;
            border: 3px solid #2d5016;
            border-radius: 12px;
            padding: 24px;
            width: 90%;
            max-width: 320px;
            max-height: 60vh;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            transform: scale(0.8) translateY(20px);
            opacity: 0;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            flex-direction: column;
        }

        .marker-dialog.show .marker-dialog-content {
            transform: scale(1) translateY(0);
            opacity: 1;
        }

        .marker-delete-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 30px;
            height: 30px;
            background: #dc3545;
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            z-index: 1;
        }

        .marker-delete-btn:hover {
            background: #c82333;
            transform: scale(1.1);
        }

        .marker-delete-btn .material-icons {
            font-size: 16px;
        }

        .marker-dialog-scrollable {
            flex: 1;
            overflow-y: auto;
            margin-bottom: 16px;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .marker-dialog-scrollable::-webkit-scrollbar {
            display: none;
        }

        .marker-dialog-fixed {
            flex-shrink: 0;
            border-top: 1px solid #e0e0e0;
            padding-top: 16px;
        }

        .marker-dialog-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
            font-family: 'Google Sans', sans-serif;
        }

        .marker-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            margin-bottom: 16px;
            font-family: 'Google Sans', sans-serif;
            box-sizing: border-box;
        }

        .marker-input:focus {
            outline: none;
            border-color: #2d5016;
        }

        .marker-dialog-buttons {
            display: flex;
            gap: 12px;
            justify-content: center;
        }

        .marker-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            font-family: 'Google Sans', sans-serif;
            transition: all 0.3s ease;
        }

        .marker-btn.cancel {
            background: #f0f0f0;
            color: #666;
        }

        .marker-btn.confirm, .marker-btn.close {
            background: #2d5016;
            color: white;
        }

        .marker-btn:hover {
            transform: translateY(-2px);
        }

        .marker-info-username {
            font-size: 14px;
            font-weight: 500;
            color: #2d5016;
            margin-bottom: 8px;
            font-family: 'Google Sans', sans-serif;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .marker-user-avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            object-fit: cover;
        }

        .marker-user-avatar-placeholder {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .marker-info-problem {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
            font-family: 'Google Sans', sans-serif;
        }

        .marker-info-comment {
            font-size: 14px;
            color: #666;
            margin-bottom: 20px;
            font-family: 'Google Sans', sans-serif;
        }

        .marker-comments-section {
            padding-top: 16px;
            margin-top: 16px;
        }

        .marker-comment-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            margin-bottom: 12px;
            font-family: 'Google Sans', sans-serif;
            box-sizing: border-box;
        }

        .marker-comment-input:focus {
            outline: none;
            border-color: #2d5016;
        }

        .marker-comment {
            display: flex;
            align-items: flex-start;
            margin-bottom: 12px;
            padding: 8px 0;
            word-wrap: break-word;
            overflow-wrap: break-word;
            position: relative;
        }

        .comment-delete-btn {
            position: absolute;
            top: 8px;
            right: 0;
            width: 20px;
            height: 20px;
            background: #dc3545;
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 12px;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .marker-comment:hover .comment-delete-btn {
            opacity: 1;
        }

        .comment-delete-btn:hover {
            background: #c82333;
        }

        .marker-comment-avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #f0f0f0;
            margin-right: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #666;
            flex-shrink: 0;
        }

        .marker-comment-avatar-img {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            object-fit: cover;
        }

        .marker-comment-avatar-placeholder {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #666;
        }

        .marker-comment-content {
            flex: 1;
            min-width: 0;
        }

        .marker-comment-username {
            font-size: 12px;
            font-weight: 500;
            color: #2d5016;
            margin-bottom: 4px;
            font-family: 'Google Sans', sans-serif;
            word-wrap: break-word;
        }

        .marker-comment-text {
            font-size: 14px;
            color: #333;
            font-family: 'Google Sans', sans-serif;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .marker-info-problem, .marker-info-comment {
            word-wrap: break-word;
            overflow-wrap: break-word;
        }
        
        /* Language Menu Styles */
        .language-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 3000;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
            background: white;
            transform: translateX(100%);
        }

        .language-overlay.show {
            opacity: 1;
            pointer-events: auto;
            transform: translateX(0);
        }

        .language-menu {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: white;
            display: flex;
            flex-direction: column;
            opacity: 1;
            visibility: visible;
            z-index: 10001;
        }

        .language-header {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            padding: 60px 20px 20px;
            border-bottom: 1px solid #f0f0f0;
            gap: 20px;
        }

        .language-back-btn {
            width: 40px;
            height: 40px;
            border: none;
            background: #f8f9fa;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .language-back-btn:hover {
            background: #e9ecef;
            transform: scale(1.05);
        }

        .language-back-btn .material-icons {
            font-size: 20px;
            color: #333;
        }

        .language-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            font-family: 'Google Sans', sans-serif;
        }

        .language-list {
            flex: 1;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .language-item {
            padding: 16px 20px;
            border-radius: 12px;
            font-size: 16px;
            font-family: 'Google Sans', sans-serif;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #666;
        }

        .language-item:hover {
            background: rgba(45, 80, 22, 0.1);
            transform: translateX(5px);
        }

        .language-item.active {
            color: #2d5016;
            font-weight: 600;
            background: rgba(45, 80, 22, 0.1);
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-in {
            animation: fadeInUp 0.6s ease-out;
        }

        /* Loading Animation */
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
            font-size: 16px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="phone-frame">
        <!-- Registration Screen -->
        <div class="screen registration-screen active" id="registration">
            <div class="reg-title">
                <div class="reg-title-small">Регистрация в</div>
                <div class="reg-title-large"><span class="highlight">Neighbly</span></div>
            </div>
            <div class="error-message" id="reg-error"></div>
            <div class="success-message" id="reg-success"></div>
            <input type="text" class="input" placeholder="Отображаемое имя" id="display-name">
            <input type="text" class="input" placeholder="Имя пользователя в Телеграме" id="telegram">
            <input type="password" class="input" placeholder="Пароль" id="password">
            <button class="reg-button" onclick="register()">Зарегистрироваться</button>
            <button class="google-signin-btn" onclick="signInWithGoogle()">
                <svg class="google-logo" viewBox="0 0 24 24">
                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                <span class="google-signin-text">Вход через Google</span>
            </button>
            <button class="login-link-button" onclick="showLoginScreen()">Уже есть аккаунт? Войти</button>
        </div>

        <!-- Login Screen -->
        <div class="screen registration-screen" id="login">
            <div class="reg-title">
                <div class="reg-title-large"><span class="highlight">Neighbly</span></div>
            </div>
            <div class="error-message" id="login-error"></div>
            <div class="success-message" id="login-success"></div>
            <input type="text" class="input" placeholder="Имя пользователя в Телеграме" id="login-telegram">
            <input type="password" class="input" placeholder="Пароль" id="login-password">
            <button class="reg-button" onclick="login()">Войти</button>
            <button class="google-signin-btn" onclick="signInWithGoogle()">
                <svg class="google-logo" viewBox="0 0 24 24">
                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                <span class="google-signin-text">Вход через Google</span>
            </button>
            <button class="login-link-button" onclick="showRegistrationScreen()">Нет аккаунта? Зарегистрироваться</button>
        </div>
        
        <!-- Main App -->
        <div class="screen" id="main-app">
            <div class="app-container">
                <!-- Neighbly Tab Content -->
                <div class="tab-content active" id="neighbly-tab">
                    <div class="map-container">
                        <div id="map" class="loading">Загрузка карты...</div>
                    </div>

                    <!-- Neighbly Overlay -->
                    <div class="neighbly-overlay">


                        <div class="greeting-weather-row animate-in">
                            <div class="greeting">Привет!</div>
                            <div class="weather">
                                <span class="weather-icon">☀️</span>
                                <span id="weather-info">Загрузка...</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ArchNeighbly Tab Content -->
                <div class="tab-content" id="arch-tab">
                    <!-- ArchNeighbly Top Bar -->
                    <div class="arch-top-bar">
                        <button class="menu-btn" onclick="toggleSideMenu()">
                            <span class="material-icons">menu</span>
                        </button>
                        <div class="arch-header-title" id="arch-header-title">ArchNeighbly</div>
                        <button class="add-chat-btn" onclick="addNewChat()">
                            <span class="material-icons">add</span>
                        </button>
                    </div>

                    <div class="arch-content">
                        <div class="arch-content-title" id="arch-content-title">ArchNeighbly</div>
                        <div class="arch-content-text" id="arch-content-text">
                            Ваш помощник в обустройстве дома,
                            <a href="javascript:void(0)" class="more-link" onclick="showAboutProject(); return false;">подробнее...</a>
                        </div>
                    </div>

                    <!-- Chat Messages Area -->
                    <div class="chat-messages" id="chat-messages"></div>

                    <!-- White overlay to hide messages -->
                    <div class="chat-overlay"></div>

                    <!-- Chat Input (only for ArchNeighbly) -->
                    <div class="chat-input-container" id="chat-input-container">
                        <div class="chat-input" id="chat-input">
                            <button class="attach-btn" onclick="attachImage()" title="Прикрепить фото">
                                <span class="material-icons">photo_camera</span>
                            </button>
                            <div class="image-attachment-container" id="image-attachment-container">
                                <div class="attached-image" id="attached-image" style="display: none;">
                                    <img id="attached-image-preview" src="" alt="Прикрепленное фото">
                                    <button class="remove-image-btn" onclick="removeAttachedImage()">×</button>
                                </div>
                                <input type="text" placeholder="Напишите сообщение..." id="chat-message-input" onkeypress="handleChatKeyPress(event)">
                            </div>
                            <button class="send-btn" onclick="handleSendClick()">
                                <span class="material-icons">keyboard_arrow_up</span>
                            </button>
                        </div>
                        <input type="file" id="image-file-input" accept="image/*" style="display: none;" onchange="handleImageAttach(event)">
                    </div>
                </div>

                <!-- Bottom Navigation -->
                <div class="bottom-nav">
                    <button class="nav-item active" onclick="switchTab('neighbly')">
                        <span class="material-icons nav-icon">map</span>
                    </button>
                    <button class="nav-item" onclick="switchTab('arch')">
                        <span class="material-icons nav-icon">edit</span>
                    </button>
                    <button class="nav-item" onclick="switchTab('profile')">
                        <span class="material-icons nav-icon">person</span>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Side Menu Overlay -->
        <div class="side-menu-overlay" id="side-menu-overlay" onclick="closeSideMenu()">
            <div class="side-menu" onclick="event.stopPropagation()">
                <h3>Чаты</h3>
                <div class="chat-list" id="chat-list">
                    <div class="no-chats">Нет сохраненных чатов</div>
                </div>
            </div>
        </div>
        
        <!-- Profile Menu Overlay -->
        <div class="profile-overlay" id="profile-overlay" onclick="closeProfileMenu()">
            <div class="profile-menu" onclick="event.stopPropagation()">
                <!-- Profile Header with Avatar and Name -->
                <div class="profile-header">
                    <div class="profile-avatar-container">
                        <div class="profile-avatar" id="profile-avatar">
                            <span class="material-icons">person</span>
                        </div>
                        <div class="profile-edit-avatar-btn" id="profile-edit-avatar-btn" onclick="handleAvatarEdit(); event.stopPropagation();">
                            <span class="material-icons" id="avatar-edit-icon">add</span>
                        </div>
                    </div>
                    <div class="profile-username" id="profile-username" onclick="editUsername(event)">Имя пользователя</div>
                </div>

                <!-- Divider -->
                <div class="profile-divider"></div>

                <!-- Menu Items -->
                <div class="profile-menu-items">
                    <div class="profile-item" onclick="showTelegramUsernamePage()">Имя пользователя в Телеграме</div>
                    <div class="profile-item" onclick="openTelegramChannel()">Мы в Телеграме</div>
                    <div class="profile-item" onclick="showContributePage()">Внести свой вклад в проект / О проекте</div>
                    <div class="profile-item" onclick="openLanguageMenu()">
                        <span>Язык</span>
                        <span class="material-icons" style="color: #2d5016;">translate</span>
                    </div>
                    <div class="profile-item account-section" onclick="toggleAccountSection()">
                        <span style="color: #666;">Аккаунт</span>
                        <span class="material-icons account-arrow" style="color: #666;">keyboard_arrow_down</span>
                    </div>
                    <div class="account-items" id="account-items" style="display: none;">
                        <div class="profile-item account-action" onclick="logout()" style="color: #dc3545; padding-left: 20px;">Выйти из аккаунта</div>
                        <div class="profile-item account-action" onclick="showDeleteAccountDialog()" style="color: #dc3545; padding-left: 20px;">Удалить аккаунт</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Full Screen Pages -->
        <div class="full-screen-page" id="profile-details-page">
            <div class="page-header">
                <button class="back-arrow" onclick="backToProfile()">
                    <span class="material-icons">arrow_back</span>
                </button>
                <div class="page-title">Профиль</div>
            </div>
            <div class="page-content">
                <div class="page-text">
                    Здесь будут настройки профиля: аватар, отображаемое имя, возраст и имя пользователя в Телеграме.
                </div>
            </div>
        </div>

        <div class="full-screen-page" id="contribute-page">
            <div class="page-header">
                <button class="back-arrow" onclick="backToProfile()">
                    <span class="material-icons">arrow_back</span>
                </button>
                <div class="page-title">Внести вклад</div>
            </div>
            <div class="page-content">
                <div class="page-text">
                    <p>Создавайте, сообщайте и делитесь вашими мыслями о состоянии нашего города!</p>

                    <p>Приложение Neighbly было сделано как вызов нечистоте, загрязнений и ухудшения среды, в которой мы живём. Это приложение позволяет вам устанавливать на карте метки, с помощью которых вы можете поспособствовать решению проблемы, что вы увидели на улице, аллее, набережной и т. д.</p>

                    <p>А если вы уж очень занятой человек и вам не до других, попробуйте расширение ArchNeighbly - чат с ИИ для решения проблем обустройства дома. Вы навсегда забудете, как выглядел ваш дом до того, как вы открыли наш чат! После решения проблем внутри своего дома, вы и не заметите, как вас будет мучить вид сломанной лавки в соседнем дворе!</p>

                    <p>Также не забывайте переходить в наш Телеграмм-канал и оставлять реакции и комментарии. Так вы очень сильно поддерживаете авторов приложения и также несёте свой вклад в улучшении нашего любимого города!</p>

                    <p>Хорошего дня!</p>
                </div>
            </div>
        </div>

        <div class="full-screen-page" id="about-page">
            <div class="page-header">
                <button class="back-arrow" onclick="backToProfile()">
                    <span class="material-icons">arrow_back</span>
                </button>
                <div class="page-title">О проекте</div>
            </div>
            <div class="page-content">
                <div class="page-text">
                    <p>Люди не замечают не только то, что происходит в мире, они и не видят, что происходит вокруг них самих. Люди слепы к окружающей их жизни, но, чтобы человек мог рассеивать свою заботу и уют другим, очевидно, что он должен создать этот уют у себя "дома". Но что мы, люди, подразумеваем, когда говорим слово "дом"?</p>

                    <p>"То, что мы называем домом, — это в сущности любое место, которое последовательно возвращает нам важные истины, которые в широком мире игнорируются, или которые нашим рассеянным и нерешительным «я» трудно удержать." Но мало кто знает, каким-же образом можно воссоздать чувство безопасности, уюта и тепла, как воссоздать наших истин?</p>

                    <p>Наше приложение способствует подаче ответа на этот вопрос. Алгоритм, что используется в ИИ, берёт информацию не с первого сайта архитектуры, а из глубокого анализа "поведения" архитектуры, и что чувствует человек, смотря на табурет или стол в своей гостиной. Обосновывая свои рекомендации, ИИ черпает вдохновение из книги Алана Де Боттона "Архитектура Счастья" - кинга, что раскрывает "чувства" и "речь" стульев с табуретами.</p>

                    <p>С нашим приложением вы обретете покой, "беседуя" с архитектурой на простом языке. Вы поймете, чего действительно не хватало в образе вашего подоконника или дивана и что мешало, когда вы долго смотрели на себя в зеркале в ванной. Закрывая за собой входную дверь вы почувствуете лёгкий прирост счастья и чувство, что каждая часть декора или интерьера рада, что вы вернулись. После этого, вы с уверенностью сможете сказать, что это место - ваш дом.</p>
                </div>
            </div>
        </div>

        <!-- Display Name Page -->
        <div class="full-screen-page" id="display-name-page">
            <div class="page-header">
                <button class="back-arrow" onclick="backToProfile()">
                    <span class="material-icons">arrow_back</span>
                </button>
                <div class="page-title">Отображаемое имя</div>
            </div>
            <div class="page-content">
                <div id="current-display-name" class="current-value"></div>
                <div class="chat-input-container">
                    <div class="chat-input">
                        <input type="text" placeholder="Новое отображаемое имя" id="display-name-input">
                        <button class="send-btn" onclick="saveDisplayName()">
                            <span class="material-icons">keyboard_arrow_up</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Avatar Page -->
        <div class="full-screen-page" id="avatar-page">
            <div class="page-header">
                <button class="back-arrow" onclick="backToProfile()">
                    <span class="material-icons">arrow_back</span>
                </button>
                <div class="page-title">Аватар</div>
            </div>
            <div class="page-content">
                <div class="avatar-container">
                    <div class="avatar-circle" id="user-avatar">
                        <span class="material-icons">person</span>
                    </div>
                    <button class="change-photo-btn" onclick="changeAvatar()">Изменить фото</button>
                    <input type="file" id="avatar-input" accept="image/*" style="display: none;" onchange="handleAvatarChange(event)">
                </div>
            </div>
        </div>

        <!-- Age Page -->
        <div class="full-screen-page" id="age-page">
            <div class="page-header">
                <button class="back-arrow" onclick="backToProfile()">
                    <span class="material-icons">arrow_back</span>
                </button>
                <div class="page-title">Возраст</div>
            </div>
            <div class="page-content">
                <div id="current-age" class="current-value"></div>
                <div class="chat-input-container">
                    <div class="chat-input">
                        <input type="number" placeholder="Ваш возраст" id="age-input">
                        <button class="send-btn" onclick="saveAge()">
                            <span class="material-icons">keyboard_arrow_up</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Telegram Username Page -->
        <div class="full-screen-page" id="telegram-username-page">
            <div class="page-header">
                <button class="back-arrow" onclick="backToProfile()">
                    <span class="material-icons">arrow_back</span>
                </button>
                <div class="page-title">Имя пользователя в Телеграме</div>
            </div>
            <div class="page-content">
                <div id="current-telegram-username" class="current-value"></div>
                <div class="chat-input-container">
                    <div class="chat-input">
                        <input type="text" placeholder="Имя пользователя в Телеграме" id="telegram-username-input">
                        <button class="send-btn" onclick="saveTelegramUsername()">
                            <span class="material-icons">keyboard_arrow_up</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Marker Dialog -->
        <div class="marker-dialog" id="marker-dialog">
            <div class="marker-dialog-content">
                <div class="marker-dialog-title" id="marker-dialog-title">Оставить метку</div>
                <input type="text" id="marker-problem" placeholder="Опишите, что случилось" class="marker-input">
                <input type="text" id="marker-comment" placeholder="Комментарий" class="marker-input">
                <div class="marker-dialog-buttons">
                    <button class="marker-btn cancel" id="marker-dialog-cancel" onclick="closeMarkerDialog()">Отмена</button>
                    <button class="marker-btn confirm" id="marker-dialog-confirm" onclick="confirmMarker()">Подтвердить</button>
                </div>
            </div>
        </div>

        <!-- Marker Info Dialog -->
        <div class="marker-dialog" id="marker-info-dialog">
            <div class="marker-dialog-content">
                <!-- Delete button -->
                <button class="marker-delete-btn" onclick="showDeleteMarkerDialog()" title="Удалить метку">
                    <span class="material-icons">close</span>
                </button>

                <div class="marker-dialog-scrollable">
                    <div class="marker-info-username" id="marker-info-username"></div>
                    <div class="marker-info-problem" id="marker-info-problem"></div>
                    <div class="marker-info-comment" id="marker-info-comment"></div>

                    <div class="marker-comments-section">
                        <div id="marker-comments-list"></div>
                    </div>
                </div>

                <div class="marker-dialog-fixed">
                    <input type="text" class="marker-comment-input" id="marker-comment-input" placeholder="Добавить комментарий...">
                    <div class="marker-dialog-buttons">
                        <button class="marker-btn cancel" onclick="closeMarkerInfoDialog()">Закрыть</button>
                        <button class="marker-btn confirm" onclick="addMarkerComment()">Отправить</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Delete Chat Confirmation Dialog -->
        <div class="delete-dialog-overlay" id="delete-dialog-overlay">
            <div class="delete-dialog">
                <div class="delete-dialog-title" id="delete-dialog-title">Вы действительно хотите удалить этот чат?</div>
                <div class="delete-dialog-buttons">
                    <button class="delete-btn cancel" id="delete-dialog-cancel" onclick="closeDeleteDialog()">Ты меня убедил</button>
                    <button class="delete-btn confirm" id="delete-dialog-confirm" onclick="confirmDeleteChat()">Да, удаляй</button>
                </div>
            </div>
        </div>

        <!-- Delete Account Confirmation Dialog -->
        <div class="delete-dialog-overlay" id="delete-account-dialog-overlay">
            <div class="delete-dialog">
                <div class="delete-dialog-title" id="delete-account-title">Удалить аккаунт?</div>
                <p id="delete-account-text" style="text-align: center; margin: 20px 0; color: #666; font-size: 14px;">
                    Все ваши данные, чаты и настройки будут безвозвратно удалены.
                </p>
                <div class="delete-dialog-buttons">
                    <button class="delete-btn cancel" id="delete-account-cancel" onclick="closeDeleteAccountDialog()">Отмена</button>
                    <button class="delete-btn confirm" id="delete-account-confirm" onclick="confirmDeleteAccount()">Удалить аккаунт</button>
                </div>
            </div>
        </div>

        <!-- Delete Marker Confirmation Dialog -->
        <div class="delete-dialog-overlay" id="delete-marker-dialog-overlay">
            <div class="delete-dialog">
                <div class="delete-dialog-title">Удалить метку?</div>
                <p style="text-align: center; margin: 20px 0; color: #666; font-size: 14px;">
                    Метка и все комментарии к ней будут удалены.
                </p>
                <div class="delete-dialog-buttons">
                    <button class="delete-btn cancel" onclick="closeDeleteMarkerDialog()">Отмена</button>
                    <button class="delete-btn confirm" onclick="confirmDeleteMarker()">Удалить</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        let currentTab = 'neighbly';
        let previousTab = 'neighbly';
        let map;

        // User data storage
        let userData = {
            displayName: '',
            avatar: '',
            age: '',
            telegramUsername: '',
            phone: '',
            password: '',
            isLoggedIn: false,
            nextChatNumber: 1 // Счетчик для ID чатов
        };

        // Chat data storage
        let chatHistory = [];
        let currentChatMessages = [];
        let currentChatId = null;

        // Global markers storage (visible to all users)
        let globalMarkers = [];

        // Language settings
        let currentLanguage = 'ru';

        // Language translations
        const translations = {
            ru: {
                greeting: 'Привет!',
                loading: 'Загрузка...',
                register: 'Зарегистрироваться',
                login: 'Войти',
                displayName: 'Отображаемое имя',
                telegramUsername: 'Имя пользователя в Телеграме',
                password: 'Пароль',
                haveAccount: 'Уже есть аккаунт? Войти',
                noAccount: 'Нет аккаунта? Зарегистрироваться',
                googleSignIn: 'Вход через Google',
                menu: 'Меню',
                contribute: 'Внести свой вклад в проект',
                about: 'О проекте',
                telegramChannel: 'Телеграм - канал',
                logout: 'Выйти из аккаунта',
                deleteAccount: 'Удалить аккаунт',
                selectLanguage: 'Выберите язык',
                chatPlaceholder: 'Напишите сообщение...',
                archTitle: 'ArchNeighbly',
                archSubtitle: 'Ваш ИИ-помощник для дома',
                archDescription: 'Ваш помощник в обустройстве дома, подробнее...',
                profileSettings: 'Настройки профиля',
                age: 'Возраст',
                newDisplayName: 'Новое отображаемое имя',
                newAge: 'Ваш возраст',
                newTelegramUsername: 'Имя пользователя в Телеграме',
                chats: 'Чаты',
                noSavedChats: 'Нет сохраненных чатов',
                username: 'Имя пользователя',
                telegramUs: 'Мы в Телеграме',
                language: 'Язык',
                account: 'Аккаунт',
                deleteChatTitle: 'Вы действительно хотите удалить этот чат?',
                deleteChatCancel: 'Ты меня убедил',
                deleteChatConfirm: 'Да, удаляй',
                deleteAccountTitle: 'Удалить аккаунт?',
                deleteAccountText: 'Все ваши данные, чаты и настройки будут безвозвратно удалены.',
                deleteAccountCancel: 'Отмена',
                deleteAccountConfirm: 'Удалить',
                addMarkerTitle: 'Оставить метку',
                addMarkerDescription: 'Опишите, что случилось',
                addMarkerComment: 'Комментарий',
                addMarkerCancel: 'Отмена',
                addMarkerConfirm: 'Подтвердить'
            },
            en: {
                greeting: 'Hello!',
                loading: 'Loading...',
                register: 'Register',
                login: 'Login',
                displayName: 'Display Name',
                telegramUsername: 'Telegram Username',
                password: 'Password',
                haveAccount: 'Already have an account? Login',
                noAccount: 'No account? Register',
                googleSignIn: 'Sign in with Google',
                menu: 'Menu',
                contribute: 'Contribute to project',
                about: 'About project',
                telegramChannel: 'Telegram - channel',
                logout: 'Logout',
                deleteAccount: 'Delete account',
                selectLanguage: 'Select language',
                chatPlaceholder: 'Write a message...',
                archTitle: 'ArchNeighbly',
                archSubtitle: 'Your AI assistant for home',
                archDescription: 'Your home improvement assistant, learn more...',
                profileSettings: 'Profile Settings',
                age: 'Age',
                newDisplayName: 'New display name',
                newAge: 'Your age',
                newTelegramUsername: 'Telegram username',
                chats: 'Chats',
                noSavedChats: 'No saved chats',
                username: 'Username',
                telegramUs: 'We on Telegram',
                language: 'Language',
                account: 'Account',
                deleteChatTitle: 'Do you really want to delete this chat?',
                deleteChatCancel: 'You convinced me',
                deleteChatConfirm: 'Yes, delete',
                deleteAccountTitle: 'Delete account?',
                deleteAccountText: 'All your data, chats and settings will be permanently deleted.',
                deleteAccountCancel: 'Cancel',
                deleteAccountConfirm: 'Delete',
                addMarkerTitle: 'Add marker',
                addMarkerDescription: 'Describe what happened',
                addMarkerComment: 'Comment',
                addMarkerCancel: 'Cancel',
                addMarkerConfirm: 'Confirm'
            },
            es: {
                greeting: '¡Hola!',
                loading: 'Cargando...',
                register: 'Registrarse',
                login: 'Iniciar sesión',
                displayName: 'Nombre para mostrar',
                telegramUsername: 'Usuario de Telegram',
                password: 'Contraseña',
                haveAccount: '¿Ya tienes cuenta? Iniciar sesión',
                noAccount: '¿No tienes cuenta? Registrarse',
                googleSignIn: 'Iniciar sesión con Google',
                menu: 'Menú',
                contribute: 'Contribuir al proyecto',
                about: 'Acerca del proyecto',
                telegramChannel: 'Telegram - canal',
                logout: 'Cerrar sesión',
                deleteAccount: 'Eliminar cuenta',
                selectLanguage: 'Seleccionar idioma',
                chatPlaceholder: 'Escribe un mensaje...',
                archTitle: 'ArchNeighbly',
                archSubtitle: 'Tu asistente de IA para el hogar',
                archDescription: 'Tu asistente para mejorar el hogar, más información...',
                profileSettings: 'Configuración del perfil',
                age: 'Edad',
                newDisplayName: 'Nuevo nombre para mostrar',
                newAge: 'Tu edad',
                newTelegramUsername: 'Usuario de Telegram',
                chats: 'Chats',
                noSavedChats: 'No hay chats guardados',
                username: 'Nombre de usuario',
                telegramUs: 'Nosotros en Telegram',
                language: 'Idioma',
                account: 'Cuenta'
            },
            fr: {
                greeting: 'Salut!',
                loading: 'Chargement...',
                register: 'S\'inscrire',
                login: 'Se connecter',
                displayName: 'Nom d\'affichage',
                telegramUsername: 'Nom d\'utilisateur Telegram',
                password: 'Mot de passe',
                haveAccount: 'Vous avez déjà un compte? Se connecter',
                noAccount: 'Pas de compte? S\'inscrire',
                googleSignIn: 'Se connecter avec Google',
                menu: 'Menu',
                contribute: 'Contribuer au projet',
                about: 'À propos du projet',
                telegramChannel: 'Telegram - canal',
                logout: 'Se déconnecter',
                deleteAccount: 'Supprimer le compte',
                selectLanguage: 'Sélectionner la langue',
                chatPlaceholder: 'Écrivez un message...',
                archTitle: 'ArchNeighbly',
                archSubtitle: 'Votre assistant IA pour la maison',
                archDescription: 'Votre assistant pour l\'amélioration de la maison, en savoir plus...',
                profileSettings: 'Paramètres du profil',
                age: 'Âge',
                newDisplayName: 'Nouveau nom d\'affichage',
                newAge: 'Votre âge',
                newTelegramUsername: 'Nom d\'utilisateur Telegram',
                chats: 'Chats',
                noSavedChats: 'Aucun chat sauvegardé',
                username: 'Nom d\'utilisateur',
                telegramUs: 'Nous sur Telegram',
                language: 'Langue',
                account: 'Compte'
            },
            de: {
                greeting: 'Hallo!',
                loading: 'Laden...',
                register: 'Registrieren',
                login: 'Anmelden',
                displayName: 'Anzeigename',
                telegramUsername: 'Telegram-Benutzername',
                password: 'Passwort',
                haveAccount: 'Bereits ein Konto? Anmelden',
                noAccount: 'Kein Konto? Registrieren',
                googleSignIn: 'Mit Google anmelden',
                menu: 'Menü',
                contribute: 'Zum Projekt beitragen',
                about: 'Über das Projekt',
                telegramChannel: 'Telegram - Kanal',
                logout: 'Abmelden',
                deleteAccount: 'Konto löschen',
                selectLanguage: 'Sprache auswählen',
                chatPlaceholder: 'Nachricht schreiben...',
                archTitle: 'ArchNeighbly',
                archSubtitle: 'Ihr KI-Assistent für Zuhause',
                archDescription: 'Ihr Assistent für Heimverbesserungen, mehr erfahren...',
                profileSettings: 'Profileinstellungen',
                age: 'Alter',
                newDisplayName: 'Neuer Anzeigename',
                newAge: 'Ihr Alter',
                newTelegramUsername: 'Telegram-Benutzername',
                chats: 'Chats',
                noSavedChats: 'Keine gespeicherten Chats',
                username: 'Benutzername',
                telegramUs: 'Wir auf Telegram',
                language: 'Sprache',
                account: 'Konto',
                deleteChatTitle: 'Möchten Sie diesen Chat wirklich löschen?',
                deleteChatCancel: 'Du hast mich überzeugt',
                deleteChatConfirm: 'Ja, löschen',
                deleteAccountTitle: 'Konto löschen?',
                deleteAccountText: 'Alle Ihre Daten, Chats und Einstellungen werden dauerhaft gelöscht.',
                deleteAccountCancel: 'Abbrechen',
                deleteAccountConfirm: 'Löschen',
                addMarkerTitle: 'Markierung hinzufügen',
                addMarkerDescription: 'Beschreiben Sie, was passiert ist',
                addMarkerComment: 'Kommentar',
                addMarkerCancel: 'Abbrechen',
                addMarkerConfirm: 'Bestätigen'
            }
        };





        // Registration
        function register() {
            const displayName = document.getElementById('display-name').value.trim();
            const telegram = document.getElementById('telegram').value.trim();
            const password = document.getElementById('password').value.trim();

            // Clear previous messages
            hideMessages();

            // Validation
            if (!displayName || !telegram || !password) {
                showError('reg-error', 'Пожалуйста, заполните все поля');
                return;
            }

            if (password.length < 6) {
                showError('reg-error', 'Пароль должен содержать минимум 6 символов');
                return;
            }

            // Check if user already exists
            const existingUsers = getStoredUsers();
            if (existingUsers.find(user => user.telegramUsername === telegram)) {
                showError('reg-error', 'Пользователь с таким именем в Телеграме уже существует');
                return;
            }

            // Create user data
            const newUser = {
                displayName: displayName,
                telegramUsername: telegram,
                phone: '',
                age: '',
                password: password,
                avatar: '',
                registrationDate: new Date().toISOString(),
                chatHistory: []
            };

            // Save user data
            saveUserData(newUser);



            // Set current user data
            userData = {
                displayName: displayName,
                telegramUsername: telegram,
                phone: '',
                age: '',
                password: password,
                avatar: '',
                isLoggedIn: true
            };

            showSuccess('reg-success', 'Регистрация успешна! Переход в приложение...');

            // Save session
            saveSession();

            // Animate transition
            setTimeout(() => {
                document.getElementById('registration').style.transform = 'translateX(-100%)';
                setTimeout(() => {
                    document.getElementById('registration').classList.remove('active');
                    document.getElementById('main-app').classList.add('active');
                    initializeApp();
                }, 300);
            }, 1500);
        }

        // Login function
        function login() {
            const telegram = document.getElementById('login-telegram').value.trim();
            const password = document.getElementById('login-password').value.trim();

            // Clear previous messages
            hideMessages();

            // Validation
            if (!telegram || !password) {
                showError('login-error', 'Пожалуйста, заполните все поля');
                return;
            }

            // Check user credentials
            const existingUsers = getStoredUsers();
            const user = existingUsers.find(u => u.telegramUsername === telegram && u.password === password);

            if (!user) {
                showError('login-error', 'Неверные данные для входа');
                return;
            }

            // Set current user data
            userData = {
                displayName: user.displayName,
                telegramUsername: user.telegramUsername,
                phone: user.phone,
                age: user.age,
                password: user.password,
                avatar: user.avatar || '',
                isLoggedIn: true,
                nextChatNumber: user.nextChatNumber || 1
            };

            // Load user's chat history
            if (user.chatHistory) {
                chatHistory = user.chatHistory;
            }

            showSuccess('login-success', 'Вход выполнен успешно! Переход в приложение...');

            // Save session
            saveSession();

            // Animate transition
            setTimeout(() => {
                document.getElementById('login').style.transform = 'translateX(-100%)';
                setTimeout(() => {
                    document.getElementById('login').classList.remove('active');
                    document.getElementById('main-app').classList.add('active');
                    initializeApp();
                }, 300);
            }, 1500);
        }

        // Screen navigation functions
        function showLoginScreen() {
            document.getElementById('registration').classList.remove('active');
            document.getElementById('login').classList.add('active');
            hideMessages();
        }

        function showRegistrationScreen() {
            document.getElementById('login').classList.remove('active');
            document.getElementById('registration').classList.add('active');
            hideMessages();
        }

        // Google Sign-In function (placeholder)
        function signInWithGoogle() {
            // Temporary implementation - just log in as a demo user
            userData = {
                displayName: 'Google User',
                avatar: '',
                age: '',
                telegramUsername: 'google_user',
                phone: '',
                password: '',
                isLoggedIn: true,
                nextChatNumber: 1
            };

            // Hide all screens
            document.querySelectorAll('.screen').forEach(screen => {
                screen.classList.remove('active');
            });

            // Show main app
            document.getElementById('main-app').classList.add('active');

            // Initialize app
            initializeApp();

            console.log('Signed in with Google (demo)');
        }

        // Message display functions
        function showError(elementId, message) {
            const errorElement = document.getElementById(elementId);
            errorElement.textContent = message;
            errorElement.style.display = 'block';
        }

        function showSuccess(elementId, message) {
            const successElement = document.getElementById(elementId);
            successElement.textContent = message;
            successElement.style.display = 'block';
        }

        function hideMessages() {
            const messages = document.querySelectorAll('.error-message, .success-message');
            messages.forEach(msg => {
                msg.style.display = 'none';
                msg.textContent = '';
            });
        }

        // Temporary storage for user data (only until page reload)
        let tempUsers = [];

        function getStoredUsers() {
            return tempUsers;
        }

        function saveUserData(userData) {
            tempUsers.push(userData);
        }

        function updateUserData(telegramUsername, updatedData) {
            const userIndex = tempUsers.findIndex(user => user.telegramUsername === telegramUsername);

            if (userIndex !== -1) {
                tempUsers[userIndex] = { ...tempUsers[userIndex], ...updatedData };
            }
        }





        // Function to generate unique chat ID
        function generateChatId() {
            const username = userData.telegramUsername.replace('@', ''); // Убираем @ если есть
            const chatId = `${username}${userData.nextChatNumber}`;
            userData.nextChatNumber++;

            // Save updated nextChatNumber to localStorage
            updateUserData(userData.telegramUsername, { nextChatNumber: userData.nextChatNumber });

            return chatId;
        }







        // No longer loading markers from localStorage - markers are temporary

        // Initialize app
        function initializeApp() {
            loadWeather();
            initMap();

            // No longer loading markers - they are temporary

            // Set initial tab
            currentTab = 'neighbly';
            document.querySelector('[onclick="switchTab(\'neighbly\')"]').classList.add('active');
            document.getElementById('neighbly-tab').classList.add('active');
        }
        
        // Weather (static for now)
        function loadWeather() {
            // Show static weather data
            document.querySelector('.weather-icon').textContent = '☀️';
            document.getElementById('weather-info').textContent = '15°C';
        }
        
        // Initialize Leaflet Map
        // User markers storage
        let userMarkers = [];
        let currentMarker = null;

        function initMap() {
            // Initialize map centered on Tula
            map = L.map('map', {
                center: [54.1961, 37.6182],
                zoom: 13,
                zoomControl: false,
                attributionControl: false
            });

            // Add CartoDB Positron tiles (white/light theme)
            L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
                attribution: '',
                subdomains: 'abcd',
                maxZoom: 19
            }).addTo(map);

            // Add click handler for placing markers
            map.on('click', function(e) {
                // Check if click was on a marker
                if (e.originalEvent && e.originalEvent.target && e.originalEvent.target.closest('.leaflet-marker-icon')) {
                    // Click was on a marker, don't show dialog
                    return;
                }

                // Check if there's already a marker at this location
                const clickedOnExistingMarker = globalMarkers.some(marker => {
                    const distance = map.distance(e.latlng, marker.position);
                    return distance < 50; // 50 meters tolerance
                });

                if (!clickedOnExistingMarker) {
                    showMarkerDialog(e.latlng);
                }
            });
        }
        
        // Tab switching
        function switchTab(tab) {
            console.log('Switching to tab:', tab);

            // Handle profile tab specially
            if (tab === 'profile') {
                // Don't change the current screen, just show profile overlay
                showProfileMenu();
                // Don't change nav item activation - keep current tab active
                return;
            }

            // Remove active from all nav items
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // Hide all tab contents with animation
            document.querySelectorAll('.tab-content').forEach(content => {
                content.style.opacity = '0';
                content.style.transform = 'translateX(-100%)';
                setTimeout(() => {
                    content.classList.remove('active');
                }, 150);
            });

            // Show selected tab with animation
            setTimeout(() => {
                const targetTab = document.getElementById(`${tab}-tab`);
                targetTab.classList.add('active');
                targetTab.style.opacity = '0';
                targetTab.style.transform = 'translateX(100%)';

                setTimeout(() => {
                    targetTab.style.transition = 'all 0.3s ease';
                    targetTab.style.opacity = '1';
                    targetTab.style.transform = 'translateX(0)';
                }, 50);

                // Show/hide ArchNeighbly specific elements
                const archTopBar = document.querySelector('.arch-top-bar');
                if (tab === 'arch') {
                    archTopBar.style.display = 'flex';
                    archTopBar.style.visibility = 'visible';
                    archTopBar.style.opacity = '1';
                    console.log('Показываем верхнюю панель ArchNeighbly', archTopBar);

                    // Если нет активного чата, показываем стартовый экран
                    if (currentChatMessages.length === 0) {
                        const archContent = document.querySelector('.arch-content');
                        if (archContent) {
                            archContent.style.display = 'block';
                            resetArchTitle();
                        }
                    }
                } else {
                    archTopBar.style.display = 'none';
                }

            }, 150);

            // Activate selected nav item
            document.querySelector(`[onclick="switchTab('${tab}')"]`).classList.add('active');

            // Update tabs (save previous before changing current)
            previousTab = currentTab;
            currentTab = tab;

            // Refresh map if switching to Neighbly
            if (tab === 'neighbly' && map) {
                setTimeout(() => {
                    map.invalidateSize();
                }, 400);
            }

            // Apply language translations to newly shown elements
            setTimeout(() => {
                updateLanguage();
            }, 200);
        }
        
        // Side menu
        function toggleSideMenu() {
            const overlay = document.getElementById('side-menu-overlay');
            updateChatList();

            // Сбрасываем принудительные стили
            overlay.style.opacity = '';
            overlay.style.visibility = '';
            overlay.style.pointerEvents = '';

            overlay.classList.add('show');
        }

        function closeSideMenu() {
            const overlay = document.getElementById('side-menu-overlay');
            overlay.classList.remove('show');

            // Принудительно скрываем overlay
            setTimeout(() => {
                overlay.style.opacity = '0';
                overlay.style.visibility = 'hidden';
                overlay.style.pointerEvents = 'none';
            }, 100);
        }

        function restoreCurrentChatDisplay() {
            const chatContainer = document.getElementById('chat-messages');

            console.log('Восстанавливаем чат. Сообщений:', currentChatMessages.length);
            console.log('Текущие сообщения:', currentChatMessages);

            // Если есть сообщения в текущем чате, отображаем их
            if (currentChatMessages.length > 0) {
                console.log('Восстанавливаем сообщения...');

                // Скрываем контент ArchNeighbly
                document.querySelector('.arch-content').style.display = 'none';

                // Показываем заголовок ArchNeighbly
                showArchTitle();

                // НЕ очищаем контейнер, если сообщения уже есть
                // Проверяем, есть ли уже сообщения в DOM
                if (chatContainer.children.length === 0) {
                    console.log('Контейнер пустой, добавляем сообщения...');

                    // Отображаем все сообщения
                    currentChatMessages.forEach(msg => {
                        if (msg.type === 'user') {
                            const messageData = {
                                text: msg.text,
                                image: msg.image
                            };
                            addUserMessageToDisplay(messageData);
                        } else {
                            addBotMessageToDisplay(msg.text);
                        }
                    });

                    // Прокручиваем к последнему сообщению
                    setTimeout(() => {
                        chatContainer.scrollTop = chatContainer.scrollHeight;
                    }, 100);
                } else {
                    console.log('Сообщения уже отображены в DOM');
                }
            } else {
                console.log('Нет сообщений, показываем стартовый экран');
                // Если сообщений нет, показываем стартовый экран
                document.querySelector('.arch-content').style.display = 'block';
                resetArchTitle();
                chatContainer.innerHTML = '';
            }
        }

        // Profile menu
        function showProfileMenu() {
            const overlay = document.getElementById('profile-overlay');
            overlay.classList.add('show');
        }
        
        function closeProfileMenu() {
            const overlay = document.getElementById('profile-overlay');
            overlay.classList.remove('show');

            // Reset account section
            const accountItems = document.getElementById('account-items');
            const accountArrow = document.querySelector('.account-arrow');
            if (accountItems) accountItems.style.display = 'none';
            if (accountArrow) accountArrow.classList.remove('rotated');

            // Ensure the current tab remains active in navigation
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[onclick="switchTab('${currentTab}')"]`).classList.add('active');
        }

        function toggleAccountSection() {
            const accountItems = document.getElementById('account-items');
            const accountArrow = document.querySelector('.account-arrow');

            if (accountItems.style.display === 'none' || !accountItems.style.display) {
                accountItems.style.display = 'block';
                accountArrow.classList.add('rotated');
            } else {
                accountItems.style.display = 'none';
                accountArrow.classList.remove('rotated');
            }
        }

        // Username editing functions
        function editUsername(event) {
            event.stopPropagation();
            const usernameElement = document.getElementById('profile-username');
            const currentText = usernameElement.textContent;

            // Create input element
            const input = document.createElement('input');
            input.type = 'text';
            input.value = currentText;
            input.className = 'profile-username editing';
            input.style.fontSize = '20px';
            input.style.fontWeight = '600';
            input.style.color = '#2d5016';
            input.style.textAlign = 'center';
            input.style.border = 'none';
            input.style.borderRadius = '8px';
            input.style.padding = '8px 12px';
            input.style.background = 'white';
            input.style.outline = 'none';
            input.style.fontFamily = 'Google Sans, sans-serif';
            input.style.minWidth = '120px';

            // Replace element
            usernameElement.parentNode.replaceChild(input, usernameElement);
            input.focus();
            input.select();

            // Save on click outside or Enter
            function saveUsername() {
                const newText = input.value.trim() || 'Имя пользователя';
                const newUsernameElement = document.createElement('div');
                newUsernameElement.id = 'profile-username';
                newUsernameElement.className = 'profile-username';
                newUsernameElement.textContent = newText;
                newUsernameElement.onclick = editUsername;

                input.parentNode.replaceChild(newUsernameElement, input);

                // Save to userData
                userData.displayName = newText;
                saveUserData();

                // Remove event listeners
                document.removeEventListener('click', handleOutsideClick);
                input.removeEventListener('keypress', handleEnterKey);
            }

            function handleOutsideClick(e) {
                if (!input.contains(e.target)) {
                    saveUsername();
                }
            }

            function handleEnterKey(e) {
                if (e.key === 'Enter') {
                    saveUsername();
                }
            }

            // Add event listeners
            setTimeout(() => {
                document.addEventListener('click', handleOutsideClick);
                input.addEventListener('keypress', handleEnterKey);
            }, 100);
        }

        // Handle avatar edit button click
        function handleAvatarEdit() {
            // For Android WebView compatibility
            if (window.Android && window.Android.openFileChooser) {
                window.Android.openFileChooser('image/*');
            } else {
                const input = document.createElement('input');
                input.type = 'file';
                input.accept = 'image/*';
                input.onchange = function(e) {
                    const file = e.target.files[0];
                    if (file && file.type.startsWith('image/')) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            // Update avatar
                            userData.avatar = e.target.result;
                            updateUserData(userData.telegramUsername, { avatar: userData.avatar });

                            // Update display
                            updateProfileDisplay();
                            updateNavigationAvatar(e.target.result);
                        };
                        reader.readAsDataURL(file);
                    }
                };

                try {
                    input.click();
                } catch (e) {
                    // Fallback for Android WebView
                    alert('Для выбора аватарки необходимо предоставить доступ к файлам. Пожалуйста, разрешите доступ в настройках приложения.');
                }
            }
        }

        // Update profile display
        function updateProfileDisplay() {
            const usernameElement = document.getElementById('profile-username');
            const avatarElement = document.getElementById('profile-avatar');
            const editIconElement = document.getElementById('avatar-edit-icon');

            if (userData.displayName) {
                usernameElement.textContent = userData.displayName;
            }

            if (userData.avatar) {
                avatarElement.innerHTML = `<img src="${userData.avatar}" alt="Avatar">`;
                editIconElement.textContent = 'edit'; // Карандашик когда есть аватарка
            } else {
                avatarElement.innerHTML = `<span class="material-icons">person</span>`;
                editIconElement.textContent = 'add'; // Плюсик когда нет аватарки
            }
        }

        // Chat functions
        function updateChatList() {
            const chatListContainer = document.getElementById('chat-list');
            const t = translations[currentLanguage];

            if (chatHistory.length === 0) {
                chatListContainer.innerHTML = `<div class="no-chats">${t.noSavedChats}</div>`;
                return;
            }

            chatListContainer.innerHTML = '';

            chatHistory.forEach((chat, index) => {
                const chatItem = document.createElement('div');
                chatItem.className = 'chat-item';

                const timestamp = new Date(chat.timestamp).toLocaleString('ru-RU', {
                    day: '2-digit',
                    month: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                });

                chatItem.innerHTML = `
                    <div class="chat-content" onclick="openChat(${index})">
                        <div class="chat-title">${chat.title}</div>
                        <div class="chat-timestamp">${timestamp}</div>
                    </div>
                    <button class="delete-chat-btn" onclick="showDeleteDialog(${index})" title="Удалить чат">
                        <span class="material-icons">delete</span>
                    </button>
                `;

                chatListContainer.appendChild(chatItem);
            });
        }

        window.openChat = function(chatIndex) {
            console.log('=== ОТКРЫТИЕ ЧАТА ===');
            console.log('Индекс чата:', chatIndex);
            console.log('История чатов:', chatHistory);

            const chat = chatHistory[chatIndex];
            if (!chat) {
                console.error('Чат не найден по индексу:', chatIndex);
                return;
            }

            console.log('Открываем чат:', chat);

            // Сохраняем текущий чат если есть сообщения
            if (currentChatMessages.length > 0 && currentChatId !== chat.id) {
                console.log('Сохраняем текущий чат в историю');
                const currentChatTitle = currentChatMessages[0]?.text?.substring(0, 30) + '...' || 'Новый чат';
                chatHistory.push({
                    id: currentChatId || Date.now(),
                    title: currentChatTitle,
                    messages: [...currentChatMessages],
                    timestamp: new Date()
                });
            }

            // Удаляем выбранный чат из истории
            chatHistory.splice(chatIndex, 1);

            // Загружаем выбранный чат
            currentChatId = chat.id;
            currentChatMessages = [...chat.messages];
            console.log('Загружено сообщений:', currentChatMessages.length);

            // Очищаем и перестраиваем отображение чата
            const chatContainer = document.getElementById('chat-messages');
            chatContainer.innerHTML = '';

            // Переключаемся на вкладку ArchNeighbly если не на ней
            if (currentTab !== 'arch') {
                switchTab('arch');
            }

            // Если есть сообщения, показываем интерфейс чата
            if (currentChatMessages.length > 0) {
                console.log('Отображаем сообщения чата...');

                // Скрываем стартовый контент и показываем интерфейс чата
                document.querySelector('.arch-content').style.display = 'none';
                showArchTitle();

                currentChatMessages.forEach((msg, index) => {
                    console.log(`Добавляем сообщение ${index}:`, msg.type, msg.text?.substring(0, 50));

                    if (msg.type === 'user') {
                        addUserMessageToDisplay({
                            text: msg.text,
                            image: msg.image
                        });
                    } else if (msg.type === 'bot') {
                        addBotMessageToDisplay(msg.text);
                    }
                });

                // Прокручиваем к последнему сообщению
                setTimeout(() => {
                    chatContainer.scrollTop = chatContainer.scrollHeight;
                    console.log('Чат восстановлен и прокручен');
                }, 200);
            } else {
                // Если сообщений нет, показываем стартовый экран
                console.log('Чат пустой, показываем стартовый экран');
                resetArchTitle();
                const archContent = document.querySelector('.arch-content');
                archContent.style.display = 'block';
                archContent.classList.remove('hide');
            }

            // Принудительно закрываем меню
            const overlay = document.getElementById('side-menu-overlay');
            overlay.classList.remove('show');
            // Дополнительно принудительно скрываем overlay
            overlay.style.opacity = '0';
            overlay.style.visibility = 'hidden';
            overlay.style.pointerEvents = 'none';
            console.log('Меню принудительно закрыто');

            console.log('=== ЧАТ ОТКРЫТ ===');
        }

        function addNewChat() {
            console.log('=== СОЗДАНИЕ НОВОГО ЧАТА ===');

            // Сохраняем текущий чат в историю если есть сообщения
            if (currentChatMessages.length > 0) {
                console.log('Сохраняем текущий чат в историю');
                const chatTitle = currentChatMessages[0]?.text?.substring(0, 30) + '...' || 'Новый чат';
                const chatData = {
                    id: currentChatId || generateChatId(),
                    title: chatTitle,
                    messages: [...currentChatMessages],
                    timestamp: new Date()
                };

                chatHistory.push(chatData);

                // Save chat history to localStorage
                updateUserData(userData.telegramUsername, { chatHistory: chatHistory });


            }

            // Создаем новый чат с уникальным ID
            currentChatId = generateChatId();
            currentChatMessages = [];

            // Переключаемся на вкладку ArchNeighbly если не на ней
            if (currentTab !== 'arch') {
                switchTab('arch');
            }

            // Очищаем контейнер сообщений
            document.getElementById('chat-messages').innerHTML = '';

            // Показываем стартовый экран
            resetArchTitle();
            const archContent = document.querySelector('.arch-content');
            archContent.style.display = 'block';
            archContent.classList.remove('hide');

            // Очищаем прикрепленное изображение
            if (attachedImageData) {
                removeAttachedImage();
            }

            console.log('Новый чат создан');
        }



        function handleSendClick() {
            sendMessage();
        }

        async function sendMessage() {
            const input = document.getElementById('chat-message-input');
            const message = input.value.trim();
            const hasImage = attachedImageData !== null;

            if (message || hasImage) {
                // Handle first message animations
                if (currentChatMessages.length === 0) {
                    animateArchTitleUp();
                    // Also hide the arch-content completely
                    document.querySelector('.arch-content').style.display = 'none';
                }

                // Создаем объект сообщения с изображением
                const messageData = {
                    text: message,
                    image: hasImage ? attachedImageData : null
                };

                addUserMessage(messageData);
                input.value = '';

                // Очищаем прикрепленное изображение
                if (hasImage) {
                    removeAttachedImage();
                }

                // Get AI response
                try {
                    // Если есть изображение, добавляем информацию о нем в сообщение
                    let aiMessage = message;
                    if (messageData.image) {
                        aiMessage = `[Пользователь прикрепил изображение: ${messageData.image.name}] ${message}`;
                    }

                    console.log('Отправляем запрос к AI с сообщением:', aiMessage);
                    console.log('Текущая история чата:', currentChatMessages);

                    const response = await getQwenResponse(aiMessage);
                    console.log('Получен ответ от AI:', response);

                    if (response && response.trim()) {
                        addBotMessage(response);
                    } else {
                        console.log('Ответ пустой, используем fallback');
                        const fallbackResponse = getLocalFallbackResponse(message);
                        addBotMessage(fallbackResponse);
                    }
                } catch (error) {
                    console.error('Error getting AI response:', error);

                    // Используем локальный fallback при ошибках
                    const fallbackResponse = getLocalFallbackResponse(message);
                    addBotMessage(fallbackResponse);
                }
            }
        }

        async function getQwenResponse(userMessage) {
            // OpenRouter API configuration for GPT OSS 20B
            const API_KEY = '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';
            const API_URL = 'https://openrouter.ai/api/v1/chat/completions';

            // Альтернативные URL для обхода CORS (если основной не работает)
            const CORS_PROXIES = [
                'https://api.allorigins.win/raw?url=',
                'https://corsproxy.io/?',
                'https://cors-anywhere.herokuapp.com/'
            ];

            const systemPrompt = `Вы - ArchNeighbly, ИИ-помощник для обустройства дома и решения бытовых вопросов.

            Вы специализируетесь на:
            - Дизайне интерьера и архитектуре
            - Ремонте и строительстве
            - Организации пространства
            - Выборе мебели и декора
            - Создании уютной атмосферы дома
            - Решении бытовых проблем

            Отвечайте кратко, полезно и дружелюбно на русском языке. Давайте практические советы и рекомендации.
            Вдохновляйтесь принципами из книги "Архитектура Счастья" Алана де Боттона - помогайте создавать дома, которые возвращают людям важные истины и чувство покоя.

            Если вы используете теги размышления <think> или <thinking>, убедитесь, что финальный ответ пользователю находится после этих тегов и содержит только полезную информацию без технических деталей размышления.`;

            // Подготавливаем историю сообщений для API
            const messages = [{ role: 'system', content: systemPrompt }];

            // Добавляем всю историю текущего чата (включая последнее сообщение пользователя)
            currentChatMessages.forEach(msg => {
                if (msg.type === 'user') {
                    // Если это последнее сообщение пользователя, используем userMessage (может содержать информацию об изображении)
                    const isLastUserMessage = currentChatMessages.indexOf(msg) === currentChatMessages.length - 1;
                    const messageText = isLastUserMessage ? userMessage : msg.text;
                    messages.push({ role: 'user', content: messageText });
                } else if (msg.type === 'bot') {
                    messages.push({ role: 'assistant', content: msg.text });
                }
            });

            try {

                // Сначала пробуем обычный запрос
                let response;
                try {
                    response = await fetch(API_URL, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${API_KEY}`
                        },
                        body: JSON.stringify({
                            model: 'openai/gpt-oss-20b:free',
                            messages: messages,
                            temperature: 0.7
                        })
                    });
                } catch (corsError) {
                    console.log('CORS error, trying proxy approach:', corsError);

                    // Пробуем через CORS прокси
                    for (const proxy of CORS_PROXIES) {
                        try {
                            const proxyUrl = proxy + encodeURIComponent(API_URL);
                            response = await fetch(proxyUrl, {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'Authorization': `Bearer ${API_KEY}`
                                },
                                body: JSON.stringify({
                                    model: 'openai/gpt-oss-20b:free',
                                    messages: messages,
                                    temperature: 0.7
                                })
                            });

                            if (response.ok) {
                                console.log('Successfully connected via proxy:', proxy);
                                break;
                            }
                        } catch (proxyError) {
                            console.log('Proxy failed:', proxy, proxyError);
                            continue;
                        }
                    }

                    if (!response || !response.ok) {
                        throw new Error('All connection methods failed');
                    }
                }

                if (!response.ok) {
                    const errorData = await response.text();
                    console.error('OpenRouter API error:', response.status, errorData);
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log('Полный ответ от API:', data);

                if (data.choices && data.choices[0] && data.choices[0].message) {
                    let content = data.choices[0].message.content;
                    console.log('Исходный контент от API:', content);

                    // Обработка тегов размышления модели
                    content = processThinkingTags(content);
                    console.log('Контент после обработки:', content);

                    return content;
                } else {
                    console.error('Неожиданный формат ответа от API:', data);
                    throw new Error('Неожиданный формат ответа от API');
                }

            } catch (error) {
                console.error('Qwen API error:', error);

                // Проверяем тип ошибки и возвращаем соответствующий ответ
                if (error.message.includes('CORS') || error.message.includes('Failed to fetch')) {
                    // Для CORS ошибок используем fallback
                    throw error;
                }

                // Для других ошибок API тоже используем fallback
                throw error;
            }
        }

        // Переменная для хранения прикрепленного изображения
        let attachedImageData = null;

        // Функции для работы с изображениями
        function attachImage() {
            const fileInput = document.getElementById('image-file-input');

            // For Android WebView compatibility
            if (window.Android && window.Android.openImagePicker) {
                window.Android.openImagePicker();
            } else if (window.Android && window.Android.openFileChooser) {
                window.Android.openFileChooser('image/*');
            } else {
                try {
                    fileInput.click();
                } catch (e) {
                    console.log('Fallback to file input failed:', e);
                    // Try to trigger file input programmatically
                    setTimeout(() => {
                        fileInput.click();
                    }, 100);
                }
            }
        }

        function handleImageAttach(event) {
            const file = event.target.files[0];
            if (file && file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    attachedImageData = {
                        file: file,
                        dataUrl: e.target.result,
                        name: file.name,
                        size: file.size
                    };

                    // Показываем превью изображения
                    const attachedImage = document.getElementById('attached-image');
                    const imagePreview = document.getElementById('attached-image-preview');
                    const chatInput = document.getElementById('chat-input');
                    const chatInputContainer = document.getElementById('chat-input-container');

                    imagePreview.src = e.target.result;
                    attachedImage.style.display = 'block';
                    chatInput.classList.add('with-image');
                    chatInputContainer.classList.add('with-image');
                };
                reader.readAsDataURL(file);
            } else {
                alert('Пожалуйста, выберите файл изображения (JPG, PNG, GIF)');
            }
        }

        function removeAttachedImage() {
            attachedImageData = null;

            const attachedImage = document.getElementById('attached-image');
            const chatInput = document.getElementById('chat-input');
            const chatInputContainer = document.getElementById('chat-input-container');
            const fileInput = document.getElementById('image-file-input');

            attachedImage.style.display = 'none';
            chatInput.classList.remove('with-image');
            chatInputContainer.classList.remove('with-image');
            fileInput.value = '';
        }

        // Function to handle image selected from Android
        function handleAndroidImageSelection(imageDataUrl, fileName) {
            if (imageDataUrl) {
                attachedImageData = {
                    dataUrl: imageDataUrl,
                    name: fileName || 'image.jpg',
                    size: 0 // Size not available from Android
                };

                // Show image preview
                const attachedImage = document.getElementById('attached-image');
                const imagePreview = document.getElementById('attached-image-preview');
                const chatInput = document.getElementById('chat-input');
                const chatInputContainer = document.getElementById('chat-input-container');

                imagePreview.src = imageDataUrl;
                attachedImage.style.display = 'block';
                chatInput.classList.add('with-image');
                chatInputContainer.classList.add('with-image');

                console.log('Image attached from Android:', fileName);
            }
        }

        // Функция для открытия изображения в полном размере
        function openImageModal(imageSrc) {
            // Создаем модальное окно для просмотра изображения
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 9999;
                cursor: pointer;
            `;

            const img = document.createElement('img');
            img.src = imageSrc;
            img.style.cssText = `
                max-width: 90%;
                max-height: 90%;
                border-radius: 12px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
            `;

            modal.appendChild(img);
            document.body.appendChild(modal);

            // Закрываем модальное окно при клике
            modal.addEventListener('click', () => {
                document.body.removeChild(modal);
            });
        }

        // Локальный fallback для демонстрации функциональности
        function getLocalFallbackResponse(userMessage) {
            const message = userMessage.toLowerCase();

            // Простые ответы на популярные вопросы о доме
            if (message.includes('ремонт') || message.includes('починить')) {
                return 'Для начала ремонта важно составить план и определить бюджет. Начните с самого необходимого - проверьте электрику, сантехнику, затем переходите к отделке. Какой именно ремонт вас интересует? 🔧';
            }

            if (message.includes('интерьер') || message.includes('дизайн')) {
                return 'При создании интерьера помните: дом должен отражать вашу личность. Начните с цветовой палитры, добавьте растения для уюта, и не забывайте о хорошем освещении. Свет создает атмосферу! 💡';
            }

            if (message.includes('мебель') || message.includes('диван') || message.includes('стол')) {
                return 'Выбирая мебель, думайте о функциональности и комфорте. Качественная мебель - это инвестиция на годы. Измерьте пространство перед покупкой и представьте, как предмет впишется в общую композицию. 🪑';
            }

            if (message.includes('уборка') || message.includes('чистота')) {
                return 'Регулярная уборка - основа уютного дома. Создайте систему: ежедневные 15 минут на поддержание порядка лучше, чем генеральная уборка раз в месяц. Начните с самого видимого! 🧹';
            }

            if (message.includes('растения') || message.includes('цветы')) {
                return 'Растения оживляют пространство и очищают воздух! Для начинающих подойдут неприхотливые: сансевиерия, потос, замиокулькас. Они прощают ошибки в уходе и радуют глаз. 🌱';
            }

            if (message.includes('кухня') || message.includes('готовить')) {
                return 'Кухня - сердце дома! Организуйте рабочий треугольник: холодильник-плита-мойка. Храните часто используемые предметы в легкодоступных местах. И не забывайте о хорошей вентиляции! 👨‍🍳';
            }

            if (message.includes('спальня') || message.includes('сон')) {
                return 'Спальня должна способствовать отдыху. Выбирайте спокойные цвета, качественный матрас, блокируйте лишний свет. Уберите электронику - пусть это будет оазис покоя. 😴';
            }

            // Общий ответ
            return 'Отличный вопрос о доме! Помните: ваш дом должен отражать вас и приносить радость. Каждая деталь важна - от цвета стен до расположения мебели. Расскажите подробнее, что именно вас интересует? 🏠✨';
        }

        // Функция для обработки тегов размышления модели
        function processThinkingTags(content) {
            // Настройка: показывать ли размышления пользователю
            const showThinking = false; // Измените на true, если хотите показывать размышления

            console.log('Обрабатываем контент (длина):', content.length);
            console.log('Первые 300 символов:', content.substring(0, 300));

            if (!content || typeof content !== 'string') {
                console.log('Контент пустой или не строка');
                return 'Понял вас! Чем могу помочь с обустройством дома? 🏠';
            }

            if (showThinking) {
                // Показываем размышления в красивом формате
                content = content.replace(/<think>([\s\S]*?)<\/think>/gi, (match, thinkingContent) => {
                    const cleanThinking = thinkingContent.trim();
                    if (cleanThinking) {
                        return `<div class="thinking-process">💭 Размышляю: ${cleanThinking}</div>`;
                    }
                    return '';
                });
            } else {
                // Осторожное удаление тегов размышления
                // Удаляем только теги <think>...</think> и <thinking>...</thinking>
                content = content.replace(/<think>[\s\S]*?<\/think>/gi, '');
                content = content.replace(/<thinking>[\s\S]*?<\/thinking>/gi, '');
            }

            // Убираем галочки из ответа
            content = content.replace(/✓/g, '');
            content = content.replace(/✔/g, '');
            content = content.replace(/☑/g, '');
            content = content.replace(/✅/g, '');

            // Очищаем лишние пробелы и переносы строк
            content = content.replace(/^\s+|\s+$/g, '').replace(/\n\s*\n\s*\n/g, '\n\n');

            // Если после обработки остался только пустой текст, возвращаем заглушку
            if (!content.trim()) {
                console.log('Контент стал пустым после обработки');
                content = 'Понял вас! Чем могу помочь с обустройством дома? 🏠';
            }

            console.log('Результат обработки (длина):', content.length);
            console.log('Первые 200 символов результата:', content.substring(0, 200));

            return content;
        }

        function addUserMessage(messageData) {
            const messagesContainer = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message user';

            // Если есть изображение, добавляем его
            if (messageData.image) {
                const imageDiv = document.createElement('div');
                imageDiv.className = 'message-image';
                imageDiv.innerHTML = `<img src="${messageData.image.dataUrl}" alt="Прикрепленное фото" onclick="openImageModal('${messageData.image.dataUrl}')">`;
                messageDiv.appendChild(imageDiv);
            }

            // Если есть текст, добавляем его
            if (messageData.text) {
                const textDiv = document.createElement('div');
                textDiv.className = 'message-text';
                textDiv.textContent = messageData.text;
                messageDiv.appendChild(textDiv);
            }

            messagesContainer.appendChild(messageDiv);

            // Проверяем высоту сообщения и добавляем ограничение если нужно
            setTimeout(() => {
                checkMessageHeight(messageDiv);
                addTableEventHandlers(); // Добавляем обработчики для таблиц
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }, 50);

            // Add loading indicator
            showLoadingIndicator();

            const userMessage = {
                type: 'user',
                sender: 'user',
                text: messageData.text || '',
                image: messageData.image,
                timestamp: new Date().toISOString()
            };

            currentChatMessages.push(userMessage);


        }

        function addBotMessage(text) {
            // Remove loading indicator
            hideLoadingIndicator();

            const messagesContainer = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message bot';

            // Форматируем текст для лучшего отображения
            const formattedText = formatBotMessage(text);

            // Проверяем, содержит ли текст HTML (например, теги размышлений)
            if (formattedText.includes('<div class="thinking-process">') || formattedText.includes('<p>')) {
                messageDiv.innerHTML = formattedText;
            } else {
                messageDiv.innerHTML = formattedText;
            }

            messagesContainer.appendChild(messageDiv);

            // Проверяем высоту сообщения и добавляем ограничение если нужно
            setTimeout(() => {
                checkMessageHeight(messageDiv);
                addTableEventHandlers(); // Добавляем обработчики для таблиц
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }, 100);

            const botMessage = {
                type: 'bot',
                sender: 'bot',
                text: text,
                timestamp: new Date().toISOString()
            };

            currentChatMessages.push(botMessage);


        }

        // Функция для форматирования сообщений бота
        function formatBotMessage(text) {
            if (!text || typeof text !== 'string') {
                return 'Понял вас! Чем могу помочь с обустройством дома? 🏠';
            }

            // Убираем лишние пробелы в начале и конце
            text = text.trim();

            // Убираем галочки из ответов ИИ
            text = text.replace(/✓/g, '');
            text = text.replace(/✔/g, '');
            text = text.replace(/☑/g, '');

            // Обрабатываем таблицы markdown
            text = processMarkdownTables(text);

            // Обрабатываем жирный текст
            text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
            text = text.replace(/\*(.*?)\*/g, '<em>$1</em>');

            // Обрабатываем заголовки
            text = text.replace(/^### (.*$)/gm, '<h3>$1</h3>');
            text = text.replace(/^## (.*$)/gm, '<h2>$1</h2>');
            text = text.replace(/^# (.*$)/gm, '<h1>$1</h1>');

            // Обрабатываем код
            text = text.replace(/`([^`]+)`/g, '<code>$1</code>');
            text = text.replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>');

            // Обрабатываем списки
            text = processMarkdownLists(text);

            // Разбиваем на абзацы
            let paragraphs = text.split(/\n\s*\n/);

            // Очищаем и форматируем каждый абзац
            paragraphs = paragraphs
                .map(p => p.trim())
                .filter(p => p.length > 0)
                .map(p => {
                    // Если абзац уже содержит HTML теги, не оборачиваем в <p>
                    if (p.includes('<h1>') || p.includes('<h2>') || p.includes('<h3>') ||
                        p.includes('<ul>') || p.includes('<ol>') || p.includes('<table>') ||
                        p.includes('<pre>')) {
                        return p;
                    }
                    return `<p>${p}</p>`;
                });

            return paragraphs.join('');
        }

        // Функция для обработки markdown таблиц
        function processMarkdownTables(text) {
            const tableRegex = /(\|.*\|[\r\n]+\|[-\s|:]+\|[\r\n]+((\|.*\|[\r\n]*)+))/g;

            return text.replace(tableRegex, (match) => {
                const lines = match.trim().split('\n');
                if (lines.length < 3) return match;

                const headerLine = lines[0];
                const separatorLine = lines[1];
                const dataLines = lines.slice(2);

                // Парсим заголовки
                const headers = headerLine.split('|').map(h => h.trim()).filter(h => h);

                // Парсим данные
                const rows = dataLines.map(line =>
                    line.split('|').map(cell => cell.trim()).filter(cell => cell)
                ).filter(row => row.length > 0);

                // Создаем HTML таблицу с автоматической шириной колонок
                let tableHtml = '<div class="table-container"><table>';

                // Заголовки
                tableHtml += '<thead><tr>';
                headers.forEach(header => {
                    tableHtml += `<th>${header}</th>`;
                });
                tableHtml += '</tr></thead>';

                // Данные
                tableHtml += '<tbody>';
                rows.forEach(row => {
                    tableHtml += '<tr>';
                    row.forEach((cell, index) => {
                        tableHtml += `<td>${cell}</td>`;
                    });
                    // Добавляем пустые ячейки если строка короче заголовка
                    for (let i = row.length; i < headers.length; i++) {
                        tableHtml += `<td></td>`;
                    }
                    tableHtml += '</tr>';
                });
                tableHtml += '</tbody></table></div>';

                return tableHtml;
            });
        }

        // Добавляем обработчики событий для таблиц после их создания
        function addTableEventHandlers() {
            const tableContainers = document.querySelectorAll('.table-container');
            tableContainers.forEach(container => {
                container.addEventListener('touchstart', function(e) {
                    e.stopPropagation();
                }, { passive: true });

                container.addEventListener('touchmove', function(e) {
                    e.stopPropagation();
                }, { passive: true });
            });
        }

        // Функция для обработки markdown списков
        function processMarkdownLists(text) {
            // Обрабатываем нумерованные списки
            const numberedListRegex = /^(\d+)\.\s+(.+)$/gm;
            let hasNumberedList = numberedListRegex.test(text);

            if (hasNumberedList) {
                text = text.replace(/^(\d+)\.\s+(.+)$/gm, '<li>$2</li>');
                text = text.replace(/(<li>.*<\/li>)/s, '<ol>$1</ol>');
            }

            // Обрабатываем маркированные списки
            const bulletListRegex = /^[-*+]\s+(.+)$/gm;
            let hasBulletList = bulletListRegex.test(text);

            if (hasBulletList) {
                text = text.replace(/^[-*+]\s+(.+)$/gm, '<li>$1</li>');
                text = text.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>');
            }

            return text;
        }

        function handleChatKeyPress(event) {
            if (event.key === 'Enter') {
                handleSendClick();
            }
        }

        // Функции для отображения сообщений без добавления в историю (для загрузки чатов)
        function addUserMessageToDisplay(messageData) {
            const messagesContainer = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message user';

            // Если есть изображение, добавляем его
            if (messageData.image) {
                const imageDiv = document.createElement('div');
                imageDiv.className = 'message-image';
                imageDiv.innerHTML = `<img src="${messageData.image.dataUrl}" alt="Прикрепленное фото" onclick="openImageModal('${messageData.image.dataUrl}')">`;
                messageDiv.appendChild(imageDiv);
            }

            // Если есть текст, добавляем его
            if (messageData.text) {
                const textDiv = document.createElement('div');
                textDiv.className = 'message-text';
                textDiv.textContent = messageData.text;
                messageDiv.appendChild(textDiv);
            }

            messagesContainer.appendChild(messageDiv);

            // Проверяем высоту сообщения
            setTimeout(() => {
                checkMessageHeight(messageDiv);
                addTableEventHandlers(); // Добавляем обработчики для таблиц
            }, 10);
        }

        function addBotMessageToDisplay(text) {
            const messagesContainer = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message bot';

            // Форматируем текст для лучшего отображения
            const formattedText = formatBotMessage(text);

            // Проверяем, содержит ли текст HTML
            if (formattedText.includes('<div class="thinking-process">') || formattedText.includes('<p>')) {
                messageDiv.innerHTML = formattedText;
            } else {
                messageDiv.innerHTML = formattedText;
            }

            messagesContainer.appendChild(messageDiv);

            // Проверяем высоту сообщения
            setTimeout(() => {
                checkMessageHeight(messageDiv);
                addTableEventHandlers(); // Добавляем обработчики для таблиц
            }, 10);
        }



        function showAboutProject() {
            showAboutPage();
        }

        // Animation functions
        function animateArchTitleUp() {
            const archContent = document.querySelector('.arch-content');
            const headerTitle = document.getElementById('arch-header-title');

            // Hide the entire arch-content section
            archContent.classList.add('hide');
            headerTitle.classList.add('show');
        }

        function resetArchTitle() {
            const archContent = document.querySelector('.arch-content');
            const headerTitle = document.getElementById('arch-header-title');
            const contentTitle = document.getElementById('arch-content-title');
            const contentText = document.getElementById('arch-content-text');

            // Show the arch-content section again
            archContent.classList.remove('hide');
            headerTitle.classList.remove('show');

            // Сбрасываем анимации заголовка
            if (contentTitle) {
                contentTitle.classList.remove('animate-up');
            }
            if (contentText) {
                contentText.classList.remove('hide');
            }

            // ВСЕГДА показываем верхнюю панель на вкладке ArchNeighbly
            const archTopBar = document.querySelector('.arch-top-bar');
            if (archTopBar && currentTab === 'arch') {
                archTopBar.style.display = 'flex';
            }
        }

        // Loading indicator functions
        function showLoadingIndicator() {
            const messagesContainer = document.getElementById('chat-messages');
            const loadingDiv = document.createElement('div');
            loadingDiv.className = 'loading-indicator';
            loadingDiv.id = 'loading-indicator';
            loadingDiv.innerHTML = `
                <div class="loading-dot"></div>
            `;
            messagesContainer.appendChild(loadingDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function hideLoadingIndicator() {
            const loadingIndicator = document.getElementById('loading-indicator');
            if (loadingIndicator) {
                loadingIndicator.style.animation = 'fadeOut 0.3s ease-out forwards';
                setTimeout(() => {
                    if (loadingIndicator.parentNode) {
                        loadingIndicator.parentNode.removeChild(loadingIndicator);
                    }
                }, 300);
            }
        }

        // Message height management functions - DISABLED
        function checkMessageHeight(messageDiv) {
            // Убираем ограничение высоты - показываем весь контент
            // Больше не добавляем класс collapsed и кнопку "Показать больше"
        }

        function toggleMessageExpansion(messageDiv, expandBtn) {
            if (messageDiv.classList.contains('collapsed')) {
                // Раскрываем сообщение
                messageDiv.classList.remove('collapsed');
                expandBtn.textContent = 'Скрыть';
            } else {
                // Сворачиваем сообщение
                messageDiv.classList.add('collapsed');
                expandBtn.textContent = 'Показать больше';

                // Прокручиваем к началу сообщения
                messageDiv.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
        }

        // Profile menu functions
        function showProfileDetails() {
            // Don't close profile menu, just hide it temporarily and show page
            document.getElementById('profile-overlay').classList.remove('show');
            document.getElementById('profile-details-page').classList.add('show');
        }

        function showContributePage() {
            // Don't close profile menu, just hide it temporarily and show page
            document.getElementById('profile-overlay').classList.remove('show');
            document.getElementById('contribute-page').classList.add('show');

            // Ensure navigation remains correct
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[onclick="switchTab('${currentTab}')"]`).classList.add('active');
        }

        function showAboutPage() {
            // Don't close profile menu, just hide it temporarily and show page
            document.getElementById('profile-overlay').classList.remove('show');
            document.getElementById('about-page').classList.add('show');

            // Ensure navigation remains correct
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[onclick="switchTab('${currentTab}')"]`).classList.add('active');
        }

        function backToProfile() {
            // Hide all full-screen pages and return to profile menu
            document.querySelectorAll('.full-screen-page').forEach(page => {
                page.classList.remove('show');
            });
            // Show profile menu again (don't change underlying screen)
            document.getElementById('profile-overlay').classList.add('show');

            // Ensure navigation remains correct
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[onclick="switchTab('${currentTab}')"]`).classList.add('active');
        }

        // Profile page functions
        function showDisplayNamePage() {
            document.getElementById('profile-overlay').classList.remove('show');
            const page = document.getElementById('display-name-page');
            const currentValue = document.getElementById('current-display-name');

            if (userData.displayName) {
                currentValue.textContent = `Текущее имя: ${userData.displayName}`;
            } else {
                currentValue.textContent = '';
            }

            page.classList.add('show');
        }

        function showAvatarPage() {
            document.getElementById('profile-overlay').classList.remove('show');
            document.getElementById('avatar-page').classList.add('show');
        }

        function showAgePage() {
            document.getElementById('profile-overlay').classList.remove('show');
            const page = document.getElementById('age-page');
            const currentValue = document.getElementById('current-age');

            if (userData.age) {
                currentValue.textContent = `Указанный возраст: ${userData.age}`;
            } else {
                currentValue.textContent = '';
            }

            page.classList.add('show');
        }

        function showTelegramUsernamePage() {
            document.getElementById('profile-overlay').classList.remove('show');
            const page = document.getElementById('telegram-username-page');
            const currentValue = document.getElementById('current-telegram-username');

            if (userData.telegramUsername) {
                currentValue.textContent = `Текущий username: ${userData.telegramUsername}`;
            } else {
                currentValue.textContent = '';
            }

            page.classList.add('show');
        }

        // Save functions
        function saveDisplayName() {
            const input = document.getElementById('display-name-input');
            if (input.value.trim()) {
                const oldValue = userData.displayName;
                const newValue = input.value.trim();
                userData.displayName = newValue;

                // Update in localStorage
                updateUserData(userData.telegramUsername, { displayName: newValue });



                input.value = '';
                alert('Отображаемое имя сохранено!');
            }
        }

        function saveAge() {
            const input = document.getElementById('age-input');
            if (input.value && input.value > 0 && input.value <= 120) {
                const oldValue = userData.age;
                const newValue = input.value;
                userData.age = newValue;

                // Update in localStorage
                updateUserData(userData.telegramUsername, { age: newValue });



                input.value = '';
                alert('Возраст сохранен!');
            } else {
                alert('Пожалуйста, введите корректный возраст (1-120)');
            }
        }

        function saveTelegramUsername() {
            const input = document.getElementById('telegram-username-input');
            if (input.value.trim()) {
                const newUsername = input.value.trim();

                // Check if username is already taken
                const existingUsers = getStoredUsers();
                if (existingUsers.find(user => user.telegramUsername === newUsername && user.telegramUsername !== userData.telegramUsername)) {
                    alert('Это имя пользователя уже занято');
                    return;
                }

                const oldValue = userData.telegramUsername;
                userData.telegramUsername = newUsername;

                // Update in localStorage
                updateUserData(oldValue, { telegramUsername: newValue });



                input.value = '';
                alert('Имя пользователя в Телеграме сохранено!');
            }
        }

        // Avatar functions
        function changeAvatar() {
            const fileInput = document.getElementById('avatar-input');

            // For Android WebView compatibility
            if (window.Android && window.Android.openFileChooser) {
                window.Android.openFileChooser('image/*');
            } else {
                try {
                    fileInput.click();
                } catch (e) {
                    // Fallback for Android WebView
                    alert('Для выбора аватарки необходимо предоставить доступ к файлам. Пожалуйста, разрешите доступ в настройках приложения.');
                }
            }
        }

        function handleAvatarChange(event) {
            const file = event.target.files[0];
            if (file && file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const avatarCircle = document.getElementById('user-avatar');
                    avatarCircle.innerHTML = `<img src="${e.target.result}" alt="Avatar">`;

                    const oldAvatar = userData.avatar;
                    userData.avatar = e.target.result;

                    // Update in localStorage
                    updateUserData(userData.telegramUsername, { avatar: e.target.result });



                    // Обновляем аватар в навигации
                    updateNavigationAvatar(e.target.result);

                    alert('Аватар загружен!');
                };
                reader.readAsDataURL(file);
            } else {
                alert('Пожалуйста, выберите файл изображения');
            }
        }

        // Функция для обновления аватара в навигации
        function updateNavigationAvatar(avatarSrc) {
            const profileNavItem = document.querySelector('.nav-item[onclick="switchTab(\'profile\')"]');
            if (profileNavItem) {
                // Скрываем смайлик
                const icon = profileNavItem.querySelector('.material-icons');
                if (icon) {
                    icon.style.display = 'none';
                }

                // Создаем или обновляем аватар в навигации
                let avatarImg = profileNavItem.querySelector('.nav-avatar');
                if (!avatarImg) {
                    avatarImg = document.createElement('img');
                    avatarImg.className = 'nav-avatar';
                    profileNavItem.appendChild(avatarImg);
                }
                avatarImg.src = avatarSrc;

                // Добавляем класс для стилизации nav-item с аватаром
                profileNavItem.classList.add('has-avatar');
            }
        }

        // Функция для удаления аватара из навигации
        function removeNavigationAvatar() {
            const profileNavItem = document.querySelector('.nav-item[onclick="switchTab(\'profile\')"]');
            if (profileNavItem) {
                // Показываем смайлик обратно
                const icon = profileNavItem.querySelector('.material-icons');
                if (icon) {
                    icon.style.display = 'block';
                }

                // Удаляем аватар
                const avatarImg = profileNavItem.querySelector('.nav-avatar');
                if (avatarImg) {
                    avatarImg.remove();
                }

                // Убираем класс
                profileNavItem.classList.remove('has-avatar');
            }
        }

        // Telegram channel function
        function openTelegramChannel() {
            closeProfileMenu();
            window.open('https://t.me/Neighbly', '_blank');
        }

        // Marker functions
        let currentMarkerPosition = null;

        function showMarkerDialog(latlng) {
            currentMarkerPosition = latlng;
            document.getElementById('marker-dialog').classList.add('show');
            document.getElementById('marker-problem').value = '';
            document.getElementById('marker-comment').value = '';
        }

        function closeMarkerDialog() {
            document.getElementById('marker-dialog').classList.remove('show');
            currentMarkerPosition = null;
        }

        function confirmMarker() {
            const problem = document.getElementById('marker-problem').value.trim();
            const comment = document.getElementById('marker-comment').value.trim();

            if (problem && currentMarkerPosition) {
                // Create marker
                const marker = L.circleMarker([currentMarkerPosition.lat, currentMarkerPosition.lng], {
                    color: '#dc3545',
                    fillColor: '#dc3545',
                    fillOpacity: 0.8,
                    radius: 8
                }).addTo(map);

                // Store marker data
                const markerData = {
                    id: Date.now(),
                    marker: marker,
                    problem: problem,
                    comment: comment,
                    position: currentMarkerPosition,
                    username: userData.telegramUsername || 'Аноним',
                    displayName: userData.displayName || 'Аноним',
                    avatar: userData.avatar || null,
                    comments: [],
                    createdAt: new Date().toISOString()
                };

                userMarkers.push(markerData);

                // Add to global markers (temporary - not saved)
                globalMarkers.push({
                    id: markerData.id,
                    problem: markerData.problem,
                    comment: markerData.comment,
                    position: markerData.position,
                    username: markerData.username,
                    displayName: markerData.displayName,
                    avatar: markerData.avatar,
                    comments: [],
                    createdAt: markerData.createdAt
                });



                // Add click handler to show info
                marker.on('click', function() {
                    showMarkerInfo(markerData);
                });

                closeMarkerDialog();
            } else {
                alert('Пожалуйста, опишите что случилось');
            }
        }

        function showMarkerInfo(markerData) {
            currentMarker = markerData;

            // Отображаем имя пользователя с аватаром
            const usernameElement = document.getElementById('marker-info-username');
            const displayName = markerData.displayName || markerData.username;
            const username = markerData.username.startsWith('@') ? markerData.username : `@${markerData.username}`;

            if (markerData.avatar) {
                usernameElement.innerHTML = `<img src="${markerData.avatar}" class="marker-user-avatar"> ${displayName} (${username})`;
            } else {
                usernameElement.innerHTML = `<div class="marker-user-avatar-placeholder">👤</div> ${displayName} (${username})`;
            }

            document.getElementById('marker-info-problem').textContent = markerData.problem;
            document.getElementById('marker-info-comment').textContent = markerData.comment;

            // Display existing comments
            displayMarkerComments(markerData.comments);

            document.getElementById('marker-info-dialog').classList.add('show');
        }

        function displayMarkerComments(comments) {
            const commentsList = document.getElementById('marker-comments-list');
            commentsList.innerHTML = '';

            comments.forEach((comment, index) => {
                const commentDiv = document.createElement('div');
                commentDiv.className = 'marker-comment';

                // Создаем аватар
                const avatarHtml = comment.avatar
                    ? `<img src="${comment.avatar}" class="marker-comment-avatar-img">`
                    : `<div class="marker-comment-avatar-placeholder">👤</div>`;

                const displayName = comment.displayName || comment.username;
                const username = comment.username && !comment.username.startsWith('@') ? `@${comment.username}` : comment.username;
                const timeStr = comment.timestamp ? new Date(comment.timestamp).toLocaleString('ru-RU') : '';

                // Показываем кнопку удаления только для комментариев текущего пользователя
                const canDelete = userData.isLoggedIn && (comment.username === userData.telegramUsername);
                const deleteBtn = canDelete ? `<button class="comment-delete-btn" onclick="deleteComment(${index})" title="Удалить комментарий">×</button>` : '';

                commentDiv.innerHTML = `
                    <div class="marker-comment-avatar">${avatarHtml}</div>
                    <div class="marker-comment-content">
                        <div class="marker-comment-username">${displayName} (${username}) - ${timeStr}</div>
                        <div class="marker-comment-text">${comment.text}</div>
                    </div>
                    ${deleteBtn}
                `;
                commentsList.appendChild(commentDiv);
            });
        }

        function addMarkerComment() {
            const input = document.getElementById('marker-comment-input');
            const commentText = input.value.trim();

            if (commentText && currentMarker && userData.isLoggedIn) {
                const comment = {
                    username: userData.telegramUsername || 'Аноним',
                    displayName: userData.displayName || 'Аноним',
                    avatar: userData.avatar || null,
                    text: commentText,
                    timestamp: new Date().toISOString()
                };

                // Add comment only to currentMarker (which is a reference to the marker in userMarkers)
                currentMarker.comments.push(comment);

                // Update global markers - find and update the same marker (temporary)
                const globalMarkerIndex = globalMarkers.findIndex(m => m.id === currentMarker.id);
                if (globalMarkerIndex !== -1) {
                    globalMarkers[globalMarkerIndex].comments = [...currentMarker.comments];
                }

                // Send comment email
                console.log('Отправка комментария к метке:', {
                    userData: userData,
                    currentMarker: currentMarker,
                    commentText: commentText
                });



                // Refresh the display
                displayMarkerComments(currentMarker.comments);
                input.value = '';
            } else {
                if (!userData.isLoggedIn) {
                    alert('Необходимо войти в аккаунт для добавления комментариев');
                }
            }
        }

        function closeMarkerInfoDialog() {
            document.getElementById('marker-info-dialog').classList.remove('show');
            document.getElementById('marker-comment-input').value = '';
            currentMarker = null;
        }

        function showDeleteMarkerDialog() {
            document.getElementById('delete-marker-dialog-overlay').classList.add('show');
        }

        function closeDeleteMarkerDialog() {
            document.getElementById('delete-marker-dialog-overlay').classList.remove('show');
        }

        function confirmDeleteMarker() {
            if (currentMarker) {
                // Remove from map
                if (currentMarker.marker) {
                    map.removeLayer(currentMarker.marker);
                }

                // Remove from userMarkers array
                const userMarkerIndex = userMarkers.findIndex(m => m.id === currentMarker.id);
                if (userMarkerIndex !== -1) {
                    userMarkers.splice(userMarkerIndex, 1);
                }

                // Remove from globalMarkers array
                const globalMarkerIndex = globalMarkers.findIndex(m => m.id === currentMarker.id);
                if (globalMarkerIndex !== -1) {
                    globalMarkers.splice(globalMarkerIndex, 1);
                }

                // Close dialogs
                closeDeleteMarkerDialog();
                closeMarkerInfoDialog();
            }
        }

        function deleteComment(commentIndex) {
            if (currentMarker && currentMarker.comments && commentIndex >= 0 && commentIndex < currentMarker.comments.length) {
                const comment = currentMarker.comments[commentIndex];

                // Check if user can delete this comment
                if (userData.isLoggedIn && comment.username === userData.telegramUsername) {
                    if (confirm('Удалить комментарий?')) {
                        // Remove from currentMarker
                        currentMarker.comments.splice(commentIndex, 1);

                        // Update global markers
                        const globalMarkerIndex = globalMarkers.findIndex(m => m.id === currentMarker.id);
                        if (globalMarkerIndex !== -1) {
                            globalMarkers[globalMarkerIndex].comments = [...currentMarker.comments];
                        }

                        // Refresh comments display
                        displayMarkerComments(currentMarker.comments);
                    }
                }
            }
        }

        // Function to show ArchNeighbly title and interface
        function showArchTitle() {
            console.log('Показываем интерфейс ArchNeighbly');

            // Показываем верхнюю панель ArchNeighbly
            const archTopBar = document.querySelector('.arch-top-bar');
            if (archTopBar) {
                archTopBar.style.display = 'flex';
            }

            // Показываем заголовок в верхней панели
            const headerTitle = document.getElementById('arch-header-title');
            if (headerTitle) {
                headerTitle.classList.add('show');
            }

            // Анимируем заголовок контента
            const contentTitle = document.getElementById('arch-content-title');
            const contentText = document.getElementById('arch-content-text');

            if (contentTitle) {
                contentTitle.classList.add('animate-up');
            }
            if (contentText) {
                contentText.classList.add('hide');
            }
        }

        // Delete chat functions
        let chatToDelete = null;

        window.showDeleteDialog = function(chatIndex) {
            chatToDelete = chatIndex;
            document.getElementById('delete-dialog-overlay').classList.add('show');
        }

        function closeDeleteDialog() {
            document.getElementById('delete-dialog-overlay').classList.remove('show');
            chatToDelete = null;
        }

        function confirmDeleteChat() {
            if (chatToDelete !== null) {
                chatHistory.splice(chatToDelete, 1);
                updateChatList();
                closeDeleteDialog();
            }
        }
        
        // Add click animations
        document.addEventListener('click', function(e) {
            if (e.target.matches('button, .nav-item, .more-link')) {
                e.target.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    e.target.style.transform = '';
                }, 150);
            }
        });

        // Check if user is logged in (always false for temporary storage)
        function checkAuthStatus() {
            // Always show registration screen since we don't persist sessions
            document.getElementById('main-app').classList.remove('active');
            document.getElementById('login').classList.remove('active');
            document.getElementById('registration').classList.add('active');
            return false;
        }

        // Save current session (disabled for temporary storage)
        function saveSession() {
            // Do nothing - we don't persist sessions
        }

        // Logout function
        function logout() {
            // No need to remove from localStorage since we don't use it
            userData = {
                displayName: '',
                avatar: '',
                age: '',
                telegramUsername: '',
                phone: '',
                password: '',
                isLoggedIn: false,
                nextChatNumber: 1
            };
            chatHistory = [];
            currentChatMessages = [];

            // Close profile menu first
            closeProfileMenu();

            // Wait a bit for menu to close, then switch screens
            setTimeout(() => {
                // Hide all screens first
                document.querySelectorAll('.screen').forEach(screen => {
                    screen.classList.remove('active');
                });

                // Show registration screen
                const regScreen = document.getElementById('registration');
                if (regScreen) {
                    regScreen.classList.add('active');
                    console.log('Registration screen shown after logout');
                } else {
                    console.error('Registration screen not found!');
                }

                // Clear any messages
                hideMessages();

                // Clear input fields
                const displayNameField = document.getElementById('display-name');
                const telegramField = document.getElementById('telegram');
                const passwordField = document.getElementById('password');
                const loginTelegramField = document.getElementById('login-telegram');
                const loginPasswordField = document.getElementById('login-password');

                if (displayNameField) displayNameField.value = '';
                if (telegramField) telegramField.value = '';
                if (passwordField) passwordField.value = '';
                if (loginTelegramField) loginTelegramField.value = '';
                if (loginPasswordField) loginPasswordField.value = '';

                // Reset navigation avatar
                removeNavigationAvatar();
            }, 300);
        }

        // Initialize the app
        document.addEventListener('DOMContentLoaded', function() {
            // Load saved language
            const savedLanguage = localStorage.getItem('selectedLanguage');
            if (savedLanguage && translations[savedLanguage]) {
                currentLanguage = savedLanguage;
            }

            // Apply language translations
            updateLanguage();

            // Update active language in menu
            document.querySelectorAll('.language-item').forEach(item => {
                item.classList.remove('active');
            });
            const activeLanguageItem = document.querySelector(`[onclick="selectLanguage('${currentLanguage}')"]`);
            if (activeLanguageItem) {
                activeLanguageItem.classList.add('active');
            }

            // Check authentication status first
            const isLoggedIn = checkAuthStatus();

            if (isLoggedIn) {
                switchTab('neighbly');
                loadUserData();

                // Обновляем отображение профиля
                setTimeout(() => {
                    updateProfileDisplay();
                    if (userData.avatar) {
                        updateNavigationAvatar(userData.avatar);
                    }
                }, 100);
            }

            // Отладочные функции для проверки кликабельности
            setTimeout(() => {
                debugChatInput();
            }, 2000);
        });

        // Функция для отладки проблем с полем ввода
        function debugChatInput() {
            const chatInput = document.getElementById('chat-message-input');
            const chatInputContainer = document.getElementById('chat-input-container');
            const chatInputDiv = document.getElementById('chat-input');
            
            if (chatInput) {
                console.log('=== ОТЛАДКА ПОЛЯ ВВОДА ===');
                console.log('Поле найдено:', chatInput);
                console.log('Стили поля:', window.getComputedStyle(chatInput));
                console.log('pointer-events:', window.getComputedStyle(chatInput).pointerEvents);
                console.log('z-index:', window.getComputedStyle(chatInput).zIndex);
                console.log('display:', window.getComputedStyle(chatInput).display);
                console.log('visibility:', window.getComputedStyle(chatInput).visibility);
                
                // Проверяем родительские элементы
                if (chatInputContainer) {
                    console.log('Контейнер pointer-events:', window.getComputedStyle(chatInputContainer).pointerEvents);
                    console.log('Контейнер z-index:', window.getComputedStyle(chatInputContainer).zIndex);
                }
                
                if (chatInputDiv) {
                    console.log('Chat input div pointer-events:', window.getComputedStyle(chatInputDiv).pointerEvents);
                    console.log('Chat input div z-index:', window.getComputedStyle(chatInputDiv).zIndex);
                }
                
                // Добавляем обработчики событий для отладки
                chatInput.addEventListener('click', function(e) {
                    console.log('КЛИК ПО ПОЛЮ ВВОДА СРАБОТАЛ!');
                    console.log('Event:', e);
                    chatInput.focus();
                });
                
                chatInput.addEventListener('focus', function(e) {
                    console.log('ФОКУС НА ПОЛЕ ВВОДА!');
                });
                
                chatInput.addEventListener('mousedown', function(e) {
                    console.log('MOUSEDOWN НА ПОЛЕ ВВОДА!');
                });
                
                chatInput.addEventListener('touchstart', function(e) {
                    console.log('TOUCHSTART НА ПОЛЕ ВВОДА!');
                });
                
                // Принудительно делаем поле кликабельным
                chatInput.style.pointerEvents = 'auto';
                chatInput.style.userSelect = 'auto';
                chatInput.style.webkitUserSelect = 'auto';
                chatInput.style.zIndex = '9999';
                chatInput.style.position = 'relative';
                
                console.log('=== КОНЕЦ ОТЛАДКИ ===');
            } else {
                console.log('ПОЛЕ ВВОДА НЕ НАЙДЕНО!');
            }
        }

        // Delete account functions
        function showDeleteAccountDialog() {
            closeProfileMenu();
            document.getElementById('delete-account-dialog-overlay').classList.add('show');
        }

        function closeDeleteAccountDialog() {
            document.getElementById('delete-account-dialog-overlay').classList.remove('show');
        }

        function confirmDeleteAccount() {
            // Remove user from temporary storage
            const userIndex = tempUsers.findIndex(user => user.telegramUsername === userData.telegramUsername);
            if (userIndex !== -1) {
                tempUsers.splice(userIndex, 1);
            }



            // Reset user data
            userData = {
                displayName: '',
                avatar: '',
                age: '',
                telegramUsername: '',
                phone: '',
                password: '',
                isLoggedIn: false,
                nextChatNumber: 1
            };
            chatHistory = [];
            currentChatMessages = [];

            // Close dialog and show registration screen
            closeDeleteAccountDialog();

            setTimeout(() => {
                document.querySelectorAll('.screen').forEach(screen => {
                    screen.classList.remove('active');
                });

                const regScreen = document.getElementById('registration');
                if (regScreen) {
                    regScreen.classList.add('active');
                    console.log('Registration screen shown after account deletion');
                } else {
                    console.error('Registration screen not found after account deletion!');
                }

                hideMessages();
                removeNavigationAvatar();

                // Show success message
                showSuccess('reg-success', 'Аккаунт успешно удален');
            }, 300);
        }



        // Language functions
        function openLanguageMenu() {
            const overlay = document.getElementById('language-overlay');

            // Close profile menu first
            closeProfileMenu();

            // Show language menu with slide animation
            setTimeout(() => {
                overlay.classList.add('show');
            }, 300);
        }

        function closeLanguageMenu() {
            const overlay = document.getElementById('language-overlay');

            // Hide language menu
            overlay.classList.remove('show');

            // Show profile menu again after animation
            setTimeout(() => {
                showProfileMenu();
            }, 300);
        }

        function selectLanguage(lang) {
            if (lang === currentLanguage) return;

            currentLanguage = lang;

            // Save selected language
            localStorage.setItem('selectedLanguage', lang);

            updateLanguage();

            // Update active language in menu
            document.querySelectorAll('.language-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[onclick="selectLanguage('${lang}')"]`).classList.add('active');

            // Close menu after selection
            setTimeout(() => {
                closeLanguageMenu();
            }, 500);
        }

        function updateLanguage() {
            const t = translations[currentLanguage];

            // Update greeting
            const greeting = document.querySelector('.greeting');
            if (greeting) greeting.textContent = t.greeting;

            // Update registration screen
            const regButtons = document.querySelectorAll('.reg-button');
            regButtons.forEach(button => {
                if (button.onclick && button.onclick.toString().includes('register')) {
                    button.textContent = t.register;
                } else if (button.onclick && button.onclick.toString().includes('login')) {
                    button.textContent = t.login;
                }
            });

            // Update Google sign-in buttons
            const googleButtons = document.querySelectorAll('.google-signin-text');
            googleButtons.forEach(span => {
                span.textContent = t.googleSignIn;
            });

            // Update login/register links
            const loginLinks = document.querySelectorAll('.login-link-button');
            loginLinks.forEach(link => {
                if (link.onclick && link.onclick.toString().includes('showLoginScreen')) {
                    link.textContent = t.haveAccount;
                } else if (link.onclick && link.onclick.toString().includes('showRegistrationScreen')) {
                    link.textContent = t.noAccount;
                }
            });

            // Update input placeholders
            const displayNameInput = document.getElementById('display-name');
            if (displayNameInput) displayNameInput.placeholder = t.displayName;

            const telegramInput = document.getElementById('telegram');
            if (telegramInput) telegramInput.placeholder = t.telegramUsername;

            const passwordInput = document.getElementById('password');
            if (passwordInput) passwordInput.placeholder = t.password;

            const loginTelegramInput = document.getElementById('login-telegram');
            if (loginTelegramInput) loginTelegramInput.placeholder = t.telegramUsername;

            const loginPasswordInput = document.getElementById('login-password');
            if (loginPasswordInput) loginPasswordInput.placeholder = t.password;

            // Update chat input placeholder
            const chatInput = document.getElementById('chat-message-input');
            if (chatInput) chatInput.placeholder = t.chatPlaceholder;

            // Update ArchNeighbly title and subtitle
            const archTitle = document.querySelector('.arch-title');
            if (archTitle) archTitle.textContent = t.archTitle;

            const archSubtitle = document.querySelector('.arch-subtitle');
            if (archSubtitle) archSubtitle.textContent = t.archSubtitle;

            // Update ArchNeighbly description
            const archDescription = document.querySelector('.arch-description');
            if (archDescription) archDescription.textContent = t.archDescription;

            // Update profile menu
            const profileTitle = document.querySelector('.profile-title');
            if (profileTitle) profileTitle.textContent = t.menu;

            // Update profile settings inputs
            const displayNameProfileInput = document.getElementById('display-name-input');
            if (displayNameProfileInput) displayNameProfileInput.placeholder = t.newDisplayName;

            const ageInput = document.getElementById('age-input');
            if (ageInput) ageInput.placeholder = t.newAge;

            const telegramUsernameInput = document.getElementById('telegram-username-input');
            if (telegramUsernameInput) telegramUsernameInput.placeholder = t.newTelegramUsername;

            // Update language menu title
            const languageTitle = document.querySelector('.language-title');
            if (languageTitle) languageTitle.textContent = t.selectLanguage;

            // Update chats page title
            const chatsTitle = document.querySelector('.side-menu h3');
            if (chatsTitle) chatsTitle.textContent = t.chats;

            // Update "No saved chats" message
            const noChatsMessage = document.querySelector('.no-chats');
            if (noChatsMessage) noChatsMessage.textContent = t.noSavedChats;

            // Update ArchNeighbly description with link
            const archContentText = document.querySelector('.arch-content-text');
            if (archContentText) {
                const linkText = {
                    'ru': 'подробнее...',
                    'en': 'learn more...',
                    'es': 'más información...',
                    'fr': 'en savoir plus...',
                    'de': 'mehr erfahren...'
                };
                const currentLinkText = linkText[currentLanguage] || 'подробнее...';
                archContentText.innerHTML = `${t.archDescription.replace(currentLinkText, `<a href="javascript:void(0)" class="more-link" onclick="showAboutProject(); return false;">${currentLinkText}</a>`)}`;
            }

            // Update navigation tab labels
            updateNavigationLabels(t);

            // Update menu items by data attributes or specific selectors
            updateMenuItems(t);

            // Update dialog texts
            updateDialogTexts(t);
        }

        function updateDialogTexts(t) {
            // Update delete chat dialog
            const deleteChatTitle = document.getElementById('delete-dialog-title');
            if (deleteChatTitle) deleteChatTitle.textContent = t.deleteChatTitle;

            const deleteChatCancel = document.getElementById('delete-dialog-cancel');
            if (deleteChatCancel) deleteChatCancel.textContent = t.deleteChatCancel;

            const deleteChatConfirm = document.getElementById('delete-dialog-confirm');
            if (deleteChatConfirm) deleteChatConfirm.textContent = t.deleteChatConfirm;

            // Update delete account dialog
            const deleteAccountTitle = document.getElementById('delete-account-title');
            if (deleteAccountTitle) deleteAccountTitle.textContent = t.deleteAccountTitle;

            const deleteAccountText = document.getElementById('delete-account-text');
            if (deleteAccountText) deleteAccountText.textContent = t.deleteAccountText;

            const deleteAccountCancel = document.getElementById('delete-account-cancel');
            if (deleteAccountCancel) deleteAccountCancel.textContent = t.deleteAccountCancel;

            const deleteAccountConfirm = document.getElementById('delete-account-confirm');
            if (deleteAccountConfirm) deleteAccountConfirm.textContent = t.deleteAccountConfirm;

            // Update marker dialog
            const markerDialogTitle = document.getElementById('marker-dialog-title');
            if (markerDialogTitle) markerDialogTitle.textContent = t.addMarkerTitle;

            const markerProblem = document.getElementById('marker-problem');
            if (markerProblem) markerProblem.placeholder = t.addMarkerDescription;

            const markerComment = document.getElementById('marker-comment');
            if (markerComment) markerComment.placeholder = t.addMarkerComment;

            const markerDialogCancel = document.getElementById('marker-dialog-cancel');
            if (markerDialogCancel) markerDialogCancel.textContent = t.addMarkerCancel;

            const markerDialogConfirm = document.getElementById('marker-dialog-confirm');
            if (markerDialogConfirm) markerDialogConfirm.textContent = t.addMarkerConfirm;
        }

        function updateNavigationLabels(t) {
            // Update navigation tab labels if they exist
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                const onclick = item.getAttribute('onclick');
                if (onclick && onclick.includes('switchTab')) {
                    // Extract tab name from onclick
                    const tabMatch = onclick.match(/switchTab\('([^']+)'\)/);
                    if (tabMatch) {
                        const tabName = tabMatch[1];
                        // Update label based on tab name
                        if (tabName === 'chats') {
                            const label = item.querySelector('.nav-label');
                            if (label) label.textContent = t.chats;
                        }
                    }
                }
            });
        }

        function updateMenuItems(t) {
            // Update profile menu items
            const menuItems = document.querySelectorAll('.profile-item');
            menuItems.forEach(item => {
                const onclick = item.getAttribute('onclick');
                const text = item.textContent.trim();

                if (onclick) {
                    if (onclick.includes('showDisplayNamePage')) {
                        item.textContent = t.username;
                    } else if (onclick.includes('showAgePage')) {
                        item.textContent = t.age;
                    } else if (onclick.includes('showTelegramUsernamePage')) {
                        item.textContent = t.telegramUs;
                    } else if (onclick.includes('contribute')) {
                        item.textContent = t.contribute;
                    } else if (onclick.includes('about')) {
                        item.textContent = t.about;
                    } else if (onclick.includes('telegramChannel')) {
                        item.textContent = t.telegramChannel;
                    } else if (onclick.includes('showLanguageMenu')) {
                        item.textContent = t.language;
                    } else if (onclick.includes('logout')) {
                        item.textContent = t.logout;
                    } else if (onclick.includes('deleteAccount')) {
                        item.textContent = t.deleteAccount;
                    }
                } else {
                    // Handle items without onclick by text content
                    if (text.includes('Аккаунт') || text.includes('Account') || text.includes('Cuenta') || text.includes('Compte') || text.includes('Konto')) {
                        item.textContent = t.account;
                    }
                }
            });

            // Update page titles
            const pageTitles = document.querySelectorAll('.page-title');
            pageTitles.forEach(title => {
                const parentPage = title.closest('.page');
                if (parentPage) {
                    const pageId = parentPage.id;
                    if (pageId === 'display-name-page') {
                        title.textContent = t.displayName;
                    } else if (pageId === 'age-page') {
                        title.textContent = t.age;
                    } else if (pageId === 'telegram-username-page') {
                        title.textContent = t.telegramUsername;
                    } else if (pageId === 'profile-settings-page') {
                        title.textContent = t.profileSettings;
                    }
                }
            });
        }

        // Save session when user data changes
        function updateUserDataAndSaveSession() {
            saveSession();
            updateUserData(userData.telegramUsername, userData);
        }

        // Enable smooth scrolling
        function enableScrolling() {
            // Настраиваем прокрутку для чата
            const chatMessages = document.querySelector('.chat-messages');
            if (chatMessages) {
                chatMessages.style.touchAction = 'pan-y';
                chatMessages.style.webkitOverflowScrolling = 'touch';
                chatMessages.style.scrollBehavior = 'smooth';
            }

            // Настраиваем прокрутку для других элементов
            const scrollableElements = document.querySelectorAll('.profile-menu-items, .chat-list, .page-content');
            scrollableElements.forEach(element => {
                element.style.touchAction = 'pan-y';
                element.style.webkitOverflowScrolling = 'touch';
            });

            // Предотвращаем масштабирование при множественном касании
            document.addEventListener('touchmove', function(e) {
                if (e.touches.length > 1) {
                    e.preventDefault();
                }
            }, { passive: false });
        }

        // Инициализируем прокрутку после загрузки DOM
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(enableScrolling, 100);
        });
    </script>

    <!-- Language Menu -->
    <div class="language-overlay" id="language-overlay">
        <div class="language-menu" id="language-menu">
            <div class="language-header">
                <button class="language-back-btn" onclick="closeLanguageMenu()">
                    <span class="material-icons">arrow_back</span>
                </button>
                <div class="language-title">Выберите язык</div>
            </div>
            <div class="language-list">
                <div class="language-item active" onclick="selectLanguage('ru')">Русский</div>
                <div class="language-item" onclick="selectLanguage('en')">English</div>
                <div class="language-item" onclick="selectLanguage('es')">Español</div>
                <div class="language-item" onclick="selectLanguage('fr')">Français</div>
                <div class="language-item" onclick="selectLanguage('de')">Deutsch</div>
            </div>
        </div>
    </div>
</body>
</html>
